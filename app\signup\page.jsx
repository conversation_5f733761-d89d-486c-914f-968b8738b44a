'use client';

import { useState, useCallback } from 'react';
import <PERSON><PERSON><PERSON> from 'react-easy-crop';
import { getCroppedImg } from '../utils/cropImage';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import zxcvbn from 'zxcvbn';
import Swal from 'sweetalert2';

export default function Signup() {
    const [email, setEmail] = useState('');
    const [fullname, setFullname] = useState('');
    const [password, setPassword] = useState('');
    const [profileImage, setProfileImage] = useState(null);
    const [previewImage, setPreviewImage] = useState(null);
    const [crop, setCrop] = useState({ x: 0, y: 0 });
    const [zoom, setZoom] = useState(1);
    const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);
    const [showCropper, setShowCropper] = useState(false);
    const [strength, setStrength] = useState(0);
    const router = useRouter();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [validation, setValidation] = useState({
        length: false,
        uppercase: false,
        lowercase: false,
        number: false,
        special: false,
    });
    const [fullnameError, setFullnameError] = useState('');

    const handlePasswordChange = (e) => {
        const value = e.target.value;
        setPassword(value);
        setStrength(zxcvbn(value).score);

        // ตรวจสอบเงื่อนไข ผ่าน
        setValidation({
            length: value.length >= 8,
            uppercase: /[A-Z]/.test(value),
            lowercase: /[a-z]/.test(value),
            number: /\d/.test(value),
            special: /[@$!%*#?&^+=]/.test(value),
        });
    };

    const getStrengthLabel = () => {
        switch (strength) {
            case 0: return 'Very Weak';
            case 1: return 'Weak';
            case 2: return 'Fair';
            case 3: return 'Good';
            case 4: return 'Strong';
            default: return '';
        }
    };

    const handleImageChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            // ตรวจสอบขนาดไฟล์ (5MB)
            const maxSize = 5 * 1024 * 1024;
            if (file.size > maxSize) {
                Swal.fire({
                    icon: 'error',
                    title: 'File Too Large',
                    text: 'Profile image must not exceed 5MB.',
                    confirmButtonColor: '#ef4444'
                });
                return;
            }

            // ตรวจสอบประเภทไฟล์
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
            if (!allowedTypes.includes(file.type)) {
                Swal.fire({
                    icon: 'error',
                    title: 'Invalid File Type',
                    text: 'Only JPEG, PNG, GIF, and WebP images are allowed.',
                    confirmButtonColor: '#ef4444'
                });
                return;
            }

            setProfileImage(file);
            const reader = new FileReader();
            reader.onloadend = () => {
                setPreviewImage(reader.result);
                setShowCropper(true);
            };
            reader.readAsDataURL(file);
        }
    };

    const onCropComplete = useCallback((_, croppedPixels) => {
        setCroppedAreaPixels(croppedPixels);
    }, []);

    const cropAndSave = async () => {
        try {
            const croppedImage = await getCroppedImg(previewImage, croppedAreaPixels);

            // แปลง base64 เป็น Blob
            const response = await fetch(croppedImage);
            const blob = await response.blob();

            // ตรวจสอบขนาดไฟล์ครอป
            const maxSize = 5 * 1024 * 1024;
            if (blob.size > maxSize) {
                await Swal.fire({
                    icon: 'error',
                    title: 'Cropped Image Too Large',
                    text: 'The cropped image is still too large. Please try cropping a smaller area.',
                    confirmButtonColor: '#ef4444'
                });
                return;
            }

            // สร้างไฟล์ใหม่จากข้อมูลครอปแล้ว
            const file = new File([blob], "cropped_profile.jpg", { type: blob.type });

            setProfileImage(file);
            setPreviewImage(croppedImage);
            setShowCropper(false);

        } catch (error) {
            console.error('Error cropping image:', error);
            await Swal.fire({
                icon: 'error',
                title: 'Cropping Failed',
                text: 'Failed to crop the image. Please try again.',
                confirmButtonColor: '#ef4444'
            });
        }
    };

    const removeImage = () => {
        setProfileImage(null);
        setPreviewImage(null);
        setShowCropper(false);
        // เซ็ต input file
        const fileInput = document.querySelector('input[type="file"]');
        if (fileInput) {
            fileInput.value = '';
        }
    };

    const handleRegister = async (e) => {
        e.preventDefault();

        // ตรวจสอบความยาวของ fullname
        if (fullname.trim().length < 4) {
            setFullnameError('Full name must be at least 4 characters');
            await Swal.fire({
                icon: 'warning',
                title: 'Invalid Full Name',
                text: 'Full name must be at least 4 characters',
                confirmButtonColor: '#f97316'
            });
            return;
        } else {
            setFullnameError('');
        }

        // เช็กว่า<|im_start|>ี่แล้วยังถ้า<|im_start|>ี่ไม่ crop ให้<|im_start|>ี่ก่อน
        if (previewImage && showCropper) {
            await Swal.fire({
                icon: 'warning',
                title: 'Please crop your image first!',
                text: 'You must crop your profile image before submitting.',
                confirmButtonColor: '#f97316'
            });
            return;
        }

        // ตรวจสอบเงื่อนไข ผ่าน
        const passwordRequirements = Object.values(validation).every(Boolean);
        if (!passwordRequirements) {
            await Swal.fire({
                icon: 'warning',
                title: 'Password Requirements',
                html: `
                    <div class="text-left">
                        <p>Your password must include:</p>
                        <ul class="mt-2">
                            <li class="${validation.length ? 'text-green-500' : 'text-red-500'}">✓ At least 8 characters</li>
                            <li class="${validation.uppercase ? 'text-green-500' : 'text-red-500'}">✓ At least one uppercase letter</li>
                            <li class="${validation.lowercase ? 'text-green-500' : 'text-red-500'}">✓ At least one lowercase letter</li>
                            <li class="${validation.number ? 'text-green-500' : 'text-red-500'}">✓ At least one number</li>
                            <li class="${validation.special ? 'text-green-500' : 'text-red-500'}">✓ At least one special character (@$!%*#?&^+=)</li>
                        </ul>
                    </div>
                `,
                confirmButtonColor: '#f97316'
            });
            return;
        }

        // ตรวจสอบความแข็งแกร่งของผ่าน
        if (strength < 2) {
            const result = await Swal.fire({
                icon: 'warning',
                title: 'Weak Password',
                text: 'Your password is weak. Do you want to continue?',
                showCancelButton: true,
                confirmButtonColor: '#f97316',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Continue',
                cancelButtonText: 'Cancel'
            });
            if (!result.isConfirmed) {
                return;
            }
        }

        setIsSubmitting(true);

        const formData = new FormData();
        formData.append('email', email);
        formData.append('fullname', fullname);
        formData.append('password', password);
        if (profileImage) {
            formData.append('profileImage', profileImage);
        }

        try {
            const res = await fetch('/api/signup', {
                method: 'POST',
                body: formData
            });

            const data = await res.json();

            if (res.ok) {
                await Swal.fire({
                    icon: 'success',
                    title: 'Sign Up Successful',
                    html: `
                        <p>Your account has been created successfully.</p>
                        <p class="mt-2">Please check your email <strong>${email}</strong> to confirm your account.</p>
                        <p class="mt-2 text-sm text-gray-500">If you don't see the email, check your spam folder.</p>
                    `,
                    confirmButtonColor: '#22c55e'
                });
                router.push('/login');
            } else {
                await Swal.fire({
                    icon: 'error',
                    title: 'Sign Up Failed',
                    text: data.message || 'Unable to create your account. Please try again.',
                    confirmButtonColor: '#ef4444'
                });
            }
        } catch (error) {
            await Swal.fire({
                icon: 'error',
                title: 'Network Error',
                text: 'Something went wrong. Please check your connection and try again.',
                confirmButtonColor: '#ef4444'
            });
            console.error('Register error:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleFullnameChange = (e) => {
        const value = e.target.value;
        setFullname(value);

        // ตรวจสอบความยาวเมื่อ�んเปลี่ยนแปลง
        if (value.trim().length < 4 && value.trim().length > 0) {
            setFullnameError('Full name must be at least 4 characters');
        } else {
            setFullnameError('');
        }
    };

    return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-blue-50">
            <div className="bg-white py-4 px-8 rounded-xl shadow-lg w-96 border border-gray-100">
                <h2 className="text-3xl font-bold mb-4 text-center text-gray-800">Create User</h2>

                <form onSubmit={handleRegister} className="space-y-6" encType="multipart/form-data">
                    {/* Name */}
                    <div>
                        <label className="block text-sm font-semibold mb-2">Full Name</label>
                        <input
                            type="text"
                            value={fullname}
                            onChange={handleFullnameChange}
                            className={`w-full px-3 py-2 border ${fullnameError ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                                } rounded-md focus:outline-none focus:ring-2 duration-150 ease-in-out placeholder:text-gray-300`}
                            required
                            maxLength={100}
                            placeholder="Enter your full name"
                        />
                        {fullnameError && (
                            <p className="mt-1 text-xs text-red-500">{fullnameError}</p>
                        )}
                    </div>

                    {/* Email */}
                    <div>
                        <label className="block text-sm font-semibold mb-2">Email</label>
                        <input
                            type="email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 duration-150 ease-in-out placeholder:text-gray-300"
                            required
                            maxLength={100}
                        />
                    </div>

                    {/* Password */}
                    <div>
                        <label className="block text-sm font-semibold mb-2">Password</label>
                        <input
                            type="password"
                            value={password}
                            onChange={handlePasswordChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 duration-150 ease-in-out placeholder:text-gray-300"
                            required
                            minLength={8}
                        />
                        <div className="mt-1" title={`Password strength: ${getStrengthLabel()}`}>
                            <div className="h-1 w-full bg-gray-200 rounded">
                                <div className={`h-1 rounded transition-all duration-300 ${strength === 0 ? 'bg-red-400 w-1/5' :
                                    strength === 1 ? 'bg-orange-400 w-2/5' :
                                        strength === 2 ? 'bg-yellow-400 w-3/5' :
                                            strength === 3 ? 'bg-green-400 w-4/5' :
                                                'bg-green-600 w-full'
                                    }`}></div>
                            </div>
                            <div className="text-xs text-gray-500 mt-1">
                                Strength: <span className="font-semibold">{getStrengthLabel()}</span>
                            </div>
                        </div>

                        {/* เส่องส่วนแสดงเงื่อนไข ผ่าน */}
                        <div className="mt-2 text-xs space-y-1">
                            <p className="text-gray-600">Password must include:</p>
                            <ul className="space-y-1 pl-1">
                                <li className={`flex items-center ${validation.length ? 'text-green-600' : 'text-gray-500'}`}>
                                    <span className={`mr-1 ${validation.length ? 'text-green-600' : 'text-gray-400'}`}>
                                        {validation.length ? '✓' : '○'}
                                    </span>
                                    At least 8 characters
                                </li>
                                <li className={`flex items-center ${validation.uppercase ? 'text-green-600' : 'text-gray-500'}`}>
                                    <span className={`mr-1 ${validation.uppercase ? 'text-green-600' : 'text-gray-400'}`}>
                                        {validation.uppercase ? '✓' : '○'}
                                    </span>
                                    At least one uppercase letter
                                </li>
                                <li className={`flex items-center ${validation.lowercase ? 'text-green-600' : 'text-gray-500'}`}>
                                    <span className={`mr-1 ${validation.lowercase ? 'text-green-600' : 'text-gray-400'}`}>
                                        {validation.lowercase ? '✓' : '○'}
                                    </span>
                                    At least one lowercase letter
                                </li>
                                <li className={`flex items-center ${validation.number ? 'text-green-600' : 'text-gray-500'}`}>
                                    <span className={`mr-1 ${validation.number ? 'text-green-600' : 'text-gray-400'}`}>
                                        {validation.number ? '✓' : '○'}
                                    </span>
                                    At least one number
                                </li>
                                <li className={`flex items-center ${validation.special ? 'text-green-600' : 'text-gray-500'}`}>
                                    <span className={`mr-1 ${validation.special ? 'text-green-600' : 'text-gray-400'}`}>
                                        {validation.special ? '✓' : '○'}
                                    </span>
                                    At least one special character (@$!%*#?&^+=)
                                </li>
                            </ul>
                        </div>
                    </div>

                    {/* Upload & Preview */}
                    <div>
                        <label className="block text-sm font-semibold mb-2">
                            Profile Image
                            <span className="text-xs text-gray-500 font-normal"> (Max 5 MB.)</span>
                        </label>
                        <input
                            type="file"
                            accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
                            onChange={handleImageChange}
                            title="Select image files (Max 5MB)"
                            className="text-sm file:bg-green-100 file:px-3 file:py-1 file:rounded file:border-0 cursor-pointer w-full"
                        />
                        {previewImage && !showCropper && (
                            <div className="mt-4 flex flex-col items-center gap-2">
                                <img
                                    src={previewImage}
                                    alt="Profile Preview"
                                    className="w-24 h-24 rounded-full object-cover border shadow"
                                />
                                <div className="flex gap-2">
                                    <button
                                        type="button"
                                        onClick={() => setShowCropper(true)}
                                        className="text-xs bg-blue-500 hover:bg-blue-600 text-white rounded px-3 py-1 cursor-pointer transition"
                                    >
                                        Edit
                                    </button>
                                    <button
                                        type="button"
                                        onClick={removeImage}
                                        className="text-xs bg-red-400 hover:bg-red-500 text-white rounded px-3 py-1 cursor-pointer transition"
                                    >
                                        Remove
                                    </button>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Submit */}
                    <button
                        type="submit"
                        disabled={isSubmitting}
                        className={`w-full py-2 rounded transition font-semibold
                            ${isSubmitting
                                ? 'bg-gray-400 cursor-not-allowed text-gray-600'
                                : 'bg-green-600 hover:bg-green-700 text-white cursor-pointer'
                            }`}
                    >
                        {isSubmitting ? 'Processing...' : 'Sign Up'}
                    </button>
                </form>

                <div className="text-sm text-center mt-3 text-gray-400">
                    Already have an account?
                    <Link href="/login" className="text-green-600 hover:text-green-700 font-semibold ml-1">
                        Sign in
                    </Link>
                </div>
            </div>

            {/* Cropper Modal */}
            {showCropper && (
                <div className="fixed inset-0 z-50 bg-black/60 flex items-center justify-center transition-all duration-200">
                    <div className="bg-white p-4 rounded-lg shadow-xl max-w-md w-full mx-4">
                        <h3 className="text-lg font-semibold mb-3">Edit Profile Image</h3>
                        <div className="relative w-full h-64 bg-gray-100 rounded">
                            <Cropper
                                image={previewImage}
                                crop={crop}
                                zoom={zoom}
                                aspect={1}
                                onCropChange={setCrop}
                                onCropComplete={onCropComplete}
                                onZoomChange={setZoom}
                            />
                        </div>
                        <div className="flex justify-between items-center mt-4 gap-4">
                            <div className="flex items-center gap-2 flex-1">
                                <span className="text-xs text-gray-600">Zoom:</span>
                                <input
                                    type="range"
                                    min={1}
                                    max={3}
                                    step={0.1}
                                    value={zoom}
                                    onChange={(e) => setZoom(e.target.value)}
                                    className="flex-1 cursor-pointer"
                                />
                            </div>
                        </div>
                        <div className="mt-2">
                            <p className="text-gray-600 text-xs">
                                💡 Tip: Use your mouse wheel to zoom in and out.
                            </p>
                        </div>
                        <div className="flex gap-2 mt-4">
                            <button
                                type="button"
                                onClick={cropAndSave}
                                className="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 rounded cursor-pointer transition font-semibold"
                            >
                                Crop & Save
                            </button>
                            <button
                                type="button"
                                onClick={() => setShowCropper(false)}
                                className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 rounded cursor-pointer transition"
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}


// 'use client';

// import { useState, useCallback } from 'react';
// import Cropper from 'react-easy-crop';
// import { getCroppedImg } from '../utils/cropImage';
// import { useRouter } from 'next/navigation';
// import Link from 'next/link';
// import zxcvbn from 'zxcvbn';
// import Swal from 'sweetalert2';

// export default function Signup() {
//     const [email, setEmail] = useState('');
//     const [fullname, setFullname] = useState('');
//     const [password, setPassword] = useState('');
//     const [profileImage, setProfileImage] = useState(null);
//     const [previewImage, setPreviewImage] = useState(null);
//     const [crop, setCrop] = useState({ x: 0, y: 0 });
//     const [zoom, setZoom] = useState(1);
//     const [rotation, setRotation] = useState(0);
//     const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);
//     const [showCropper, setShowCropper] = useState(false);
//     const [strength, setStrength] = useState(0);
//     const router = useRouter();
//     const [isSubmitting, setIsSubmitting] = useState(false);


//     const handlePasswordChange = (e) => {
//         const value = e.target.value;
//         setPassword(value);
//         setStrength(zxcvbn(value).score);
//     };

//     const getStrengthLabel = () => {
//         switch (strength) {
//             case 0: return 'Very Weak';
//             case 1: return 'Weak';
//             case 2: return 'Fair';
//             case 3: return 'Good';
//             case 4: return 'Strong';
//             default: return '';
//         }
//     };

//     const handleImageChange = (e) => {
//         const file = e.target.files[0];
//         if (file) {
//             setProfileImage(file);
//             const reader = new FileReader();
//             reader.onloadend = () => {
//                 setPreviewImage(reader.result);
//                 setShowCropper(true);
//             };
//             reader.readAsDataURL(file);
//         }
//     };

//     const onCropComplete = useCallback((_, croppedPixels) => {
//         setCroppedAreaPixels(croppedPixels);
//     }, []);

//     const cropAndSave = async () => {
//         const croppedImage = await getCroppedImg(previewImage, croppedAreaPixels, rotation);
//         // croppedImage = base64 string

//         // แปลง base64 เป็น Blob ข้อมูลครอปแล้ว
//         const response = await fetch(croppedImage);
//         const blob = await response.blob();

//         // ตั้งชื่อไฟล์ (สม)
//         const file = new File([blob], "cropped_profile.jpg", { type: blob.type });

//         setProfileImage(file);
//         setPreviewImage(croppedImage);
//         setShowCropper(false);
//     };

//     const removeImage = () => {
//         setProfileImage(null);
//         setPreviewImage(null);
//         setShowCropper(false);
//     };

//     const handleRegister = async (e) => {
//         e.preventDefault();

//         // เช็กว่า
//         if (previewImage && showCropper) {
//             await Swal.fire({
//                 icon: 'warning',
//                 title: 'Please crop your image first!',
//                 text: 'You must crop your profile image before submitting.',
//                 confirmButtonColor: '#f97316'
//             });
//             return;
//         }

//         setIsSubmitting(true);  // disable ปุ่มไว้

//         const formData = new FormData();
//         formData.append('email', email);
//         formData.append('fullname', fullname);
//         formData.append('password', password);
//         if (profileImage) {
//             formData.append('profileImage', profileImage);
//         }

//         try {
//             const res = await fetch('/api/signup', {
//                 method: 'POST',
//                 body: formData
//             });

//             const data = await res.json();

//             if (res.ok) {
//                 await Swal.fire({
//                     icon: 'success',
//                     title: 'Sign Up Successful',
//                     text: 'Your account has been created.',
//                     confirmButtonColor: '#22c55e'
//                 });
//                 router.push('/login');
//             } else {
//                 await Swal.fire({
//                     icon: 'error',
//                     title: 'Sign Up Failed',
//                     text: data.message || 'Unable to create your account. Please try again.',
//                     confirmButtonColor: '#ef4444'
//                 });
//             }
//         } catch (error) {
//             await Swal.fire({
//                 icon: 'error',
//                 title: 'Error',
//                 text: 'Something went wrong. Please try again later.',
//                 confirmButtonColor: '#ef4444'
//             });
//             console.error('Register error:', error);
//         } finally {
//             setIsSubmitting(false);  // เปิดปุ่ม
//         }
//     };


//     return (
//         <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-blue-50">
//             <div className="bg-white py-4 px-8 rounded-xl shadow-lg w-96 border border-gray-100">
//                 <h2 className="text-3xl font-bold mb-4 text-center text-gray-800">Create User</h2>

//                 <form onSubmit={handleRegister} className="space-y-6" encType="multipart/form-data">
//                     {/* Name */}
//                     <div>
//                         <label className="block text-sm font-semibold mb-2">Full Name</label>
//                         <input type="text" value={fullname} onChange={(e) => setFullname(e.target.value)}
//                             className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 duration-150 ease-in-out placeholder:text-gray-300" required />
//                     </div>

//                     {/* Email */}
//                     <div>
//                         <label className="block text-sm font-semibold mb-2">Email</label>
//                         <input type="email" value={email} onChange={(e) => setEmail(e.target.value)}
//                             className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 duration-150 ease-in-out placeholder:text-gray-300" required />
//                     </div>

//                     {/* Password */}
//                     <div>
//                         <label className="block text-sm font-semibold mb-2">Password</label>
//                         <input type="password" value={password} onChange={handlePasswordChange}
//                             className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 duration-150 ease-in-out placeholder:text-gray-300" required />
//                         {/* <div className="text-xs text-gray-500 mt-1">Strength: <b>{getStrengthLabel()}</b></div> */}
//                         <div className="mt-1" title='Password strength level: Very Weak / Weak / Fair / Good / Strong.'>
//                             <div className="h-1 w-full bg-gray-200 rounded">
//                                 <div className={`h-1 rounded ${strength === 0 ? 'bg-red-400 w-1/5' :
//                                     strength === 1 ? 'bg-orange-400 w-2/5' :
//                                         strength === 2 ? 'bg-yellow-400 w-3/5' :
//                                             strength === 3 ? 'bg-green-400 w-4/5' :
//                                                 'bg-green-600 w-full'}`}></div>
//                             </div>
//                         </div>
//                     </div>

//                     {/* Upload & Preview */}
//                     <div>
//                         <label className="block text-sm font-semibold mb-2">Profile Image</label>
//                         <input type="file" accept="image/*" onChange={handleImageChange} title='Select image files ...'
//                             className="text-sm file:bg-green-100 file:px-3 file:py-1 file:rounded file:border-0 cursor-pointer" />
//                         {previewImage && !showCropper && (
//                             <div className="mt-4 flex flex-col items-center gap-2">
//                                 <img src={previewImage} alt="Preview" className="w-24 h-24 rounded-full object-cover border shadow" />
//                                 <div className="flex gap-2">
//                                     <button type="button" onClick={() => setShowCropper(true)}
//                                         className="text-xs bg-blue-500 hover:bg-blue-600 text-white rounded px-2 py-1 cursor-pointer transition ">Edit</button>
//                                     <button type="button" onClick={removeImage}
//                                         className="text-xs bg-red-400 hover:bg-red-500 text-white rounded px-2 py-1 cursor-pointer transition">Remove</button>
//                                 </div>
//                             </div>
//                         )}
//                     </div>

//                     {/* Submit */}
//                     <button
//                         type="submit"
//                         disabled={isSubmitting}
//                         className={`w-full py-2 rounded transition
//         ${isSubmitting ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700 text-white cursor-pointer'}`}>
//                         {isSubmitting ? 'Processing...' : 'Sign Up'}
//                     </button>
//                 </form>

//                 <div className="text-sm text-center mt-3 text-gray-400">
//                     Already have an account? <Link href="/login" className="text-green-600">Sign in</Link>
//                 </div>
//             </div>

//             {/* Cropper Modal */}
//             {showCropper && (
//                 <div className="fixed inset-0 z-50 bg-black/60 flex items-center justify-center transition-all duration-200">
//                     <div className="bg-white p-4 rounded shadow max-w-md w-full">
//                         <h3 className="text-lg font-semibold mb-2">Edit your user's profile image...</h3>
//                         <div className="relative w-full h-64 bg-gray-100">
//                             <Cropper
//                                 image={previewImage}
//                                 crop={crop}
//                                 zoom={zoom}
//                                 rotation={rotation}
//                                 aspect={1}
//                                 onCropChange={setCrop}
//                                 onCropComplete={onCropComplete}
//                                 onZoomChange={setZoom}
//                                 onRotationChange={setRotation}
//                             />
//                         </div>
//                         <div className="flex justify-between items-center mt-4">
//                             <input type="range" min={1} max={3} step={0.1} value={zoom}
//                                 onChange={(e) => setZoom(e.target.value)} className="w-1/2 cursor-pointer" />
//                             <button onClick={() => setRotation((prev) => prev + 90)}
//                                 className="bg-gray-300 hover:bg-gray-400 px-3 py-1 rounded cursor-pointer">↻</button>
//                         </div>
//                         <div>
//                             <p className='text-gray-600 text-xs'>Tip: Use your mouse wheel to zoom in and out.</p>
//                         </div>
//                         <div className="flex gap-2 mt-4">
//                             <button onClick={cropAndSave}
//                                 className="flex-1 bg-green-600 hover:bg-green-700 hover:text-white transition text-white py-2 rounded cursor-pointer">Crop & Save</button>
//                             <button onClick={() => setShowCropper(false)}
//                                 className="flex-1 bg-gray-300 hover:bg-gray-400 transition py-2 rounded cursor-pointer">Cancel</button>
//                         </div>
//                     </div>
//                 </div>
//             )}
//         </div>
//     );
// }



// 'use client';

// import { useState } from 'react';
// import { useRouter } from 'next/navigation';
// import Link from 'next/link';

// export default function Signup() {
//     const [email, setEmail] = useState('');
//     const [fullname, setFullname] = useState('');
//     const [password, setPassword] = useState('');
//     const router = useRouter();

//     const handleRegister = async (e) => {
//         e.preventDefault();
//         const res = await fetch('/api/auth/register', {
//             method: 'POST',
//             headers: { 'Content-Type': 'application/json' },
//             body: JSON.stringify({ email, password, fullname }),
//         });
//         if (res.ok) {
//             router.push('/login');
//         } else {
//             alert('Register failed');
//         }
//     };

//     return (
//         <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-blue-50">
//             <div className="bg-white p-8 rounded-xl shadow-lg w-96 border border-gray-100">
//                 <h2 className="text-3xl font-bold mb-8 text-center text-gray-800">
//                     Create User
//                 </h2>
//                 <form onSubmit={handleRegister} className="space-y-6">
//                     <div>
//                         <label className="block text-sm font-semibold text-gray-700 mb-2">Full Name</label>
//                         <input
//                             type="text"
//                             value={fullname}
//                             onChange={(e) => setFullname(e.target.value)}
//                             className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 duration-150 ease-in-out placeholder:text-gray-300"
//                             required
//                             placeholder="Enter your full name"
//                         />
//                     </div>
//                     <div>
//                         <label className="block text-sm font-semibold text-gray-700 mb-2">Email</label>
//                         <input
//                             type="email"
//                             value={email}
//                             onChange={(e) => setEmail(e.target.value)}
//                             className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 duration-150 ease-in-out placeholder:text-gray-300"
//                             required
//                             placeholder="<EMAIL>"
//                         />
//                     </div>
//                     <div>
//                         <label className="block text-sm font-semibold text-gray-700 mb-2">Password</label>
//                         <input
//                             type="password"
//                             value={password}
//                             onChange={(e) => setPassword(e.target.value)}
//                             className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 duration-150 ease-in-out placeholder:text-gray-300"
//                             required
//                             placeholder="••••••••"
//                         />
//                     </div>
//                     <button
//                         type="submit"
//                         className="w-full py-2 px-4 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md transition duration-150 ease-in-out cursor-pointer"
//                     >
//                         Sign Up
//                     </button>
//                 </form>
//                 <div className="text-sm text-center mt-6 text-gray-400">
//                     Already have an account?{' '}
//                     <Link href="/login" className="text-green-600 hover:text-orange-600 font-medium transition ease-in-out">
//                         Sign in
//                     </Link>
//                 </div>
//             </div>
//         </div>
//     );
// }
