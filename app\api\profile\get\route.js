import { connectToDatabase } from '@/lib/db';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

export async function POST(req) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return new Response(JSON.stringify({ message: 'Unauthorized' }), { status: 401 });
    }

    const { email } = await req.json();
    
    // ตรวจสอบว่า email ตรงกับ email ใน session
    if (email !== session.user.email) {
      return new Response(JSON.stringify({ message: 'Unauthorized' }), { status: 401 });
    }

    const pool = await connectToDatabase();
    
    const userResult = await pool.request()
      .input('email', email)
      .query(`
        SELECT Email, FullName, ProfileImagePath, ProfileImageContentType, Role, Active, CreatedDate, UpdatedDate
        FROM dbo.PMS3_Users
        WHERE Email = @email
      `);
    
    if (userResult.recordset.length === 0) {
      return new Response(JSON.stringify({ message: 'User not found' }), { status: 404 });
    }
    
    const user = userResult.recordset[0];
    console.log('User role from database:', user.Role);
    
    return new Response(JSON.stringify({ 
      user: {
        email: user.Email,
        fullName: user.FullName,
        profileImagePath: user.ProfileImagePath,
        profileImageContentType: user.ProfileImageContentType,
        role: user.Role,
        active: user.Active,
        createdDate: user.CreatedDate,
        updatedDate: user.UpdatedDate
      }
    }), { status: 200 });
    
  } catch (error) {
    console.error('Profile get error:', error);
    return new Response(JSON.stringify({ message: 'Error fetching profile' }), { status: 500 });
  }
}
