'use client';

import { useSession, signOut } from 'next-auth/react';
import Link from 'next/link';
import { useState, useEffect } from 'react';

export default function Navbar() {
  const { data: session } = useSession();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [userData, setUserData] = useState(null);

  // ข้อมูล3้ใช้จากฐานข้อมูล
  useEffect(() => {
    const fetchUserData = async () => {
      if (session?.user?.email) {
        try {
          const response = await fetch('/api/profile/get', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email: session.user.email }),
          });
          
          if (response.ok) {
            const data = await response.json();
            setUserData(data.user);
            console.log('User data from API:', data.user);
            console.log('User role from API:', data.user.role);
            console.log('User role from session:', session.user.role);
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
        }
      }
    };

    if (session?.user) {
      fetchUserData();
    }
  }, [session]);

  // ใช้ข้อมูลจากฐานข้อมูลจาก session
  const userImage = userData?.profileImagePath || session?.user?.image;
  const userName = userData?.fullName || session?.user?.name || 'User';
  const userEmail = session?.user?.email;
  const userRole = userData?.role || session?.user?.role || 'User';
  const userInitial = userName?.charAt(0) || userEmail?.charAt(0) || '?';

  //  ก์ Sign Out
  const handleSignOut = async () => {
    await signOut({ redirect: true, callbackUrl: '/' });
  };

  return (
    <nav className="bg-white shadow sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo / Brand */}
          <div className="flex-shrink-0 flex items-center">
            <Link href="/" className="text-xl font-bold text-blue-600">
              NextJS User Management
            </Link>
          </div>

          {/* User Profile Section - Desktop */}
          <div className="hidden md:flex items-center">
            {session?.user ? (
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-3">
                  {/* User Avatar */}
                  <div className="relative">
                    {userImage ? (
                      <img
                        src={userImage}
                        alt="Profile"
                        className="h-8 w-8 rounded-full object-cover border border-gray-200"
                      />
                    ) : (
                      <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center border border-gray-200">
                        <span className="text-sm font-medium text-blue-600">
                          {userInitial}
                        </span>
                      </div>
                    )}
                  </div>
                  
                  {/* User Info */}
                  <div className="flex flex-col">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-gray-700">{userName}</span>
                      <span className="text-[.6rem] px-2 py-0.5 rounded-full bg-blue-100 text-blue-800 inline-block">
                        {userRole}
                      </span>
                    </div>
                    <span className="text-xs text-gray-500">{userEmail}</span>
                  </div>
                </div>
                
                {/* Action Icons */}
                <div className="flex items-center space-x-2">
                  {/* Home Icon */}
                  <Link 
                    href="/" 
                    className="p-2 rounded-full text-green-600 hover:bg-green-50 transition-colors"
                    title="Home"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                  </Link>
                  
                  {/* Profile Icon */}
                  <Link 
                    href="/profile" 
                    className="p-2 rounded-full text-blue-600 hover:bg-blue-50 transition-colors"
                    title="Update Profile"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </Link>
                  
                  {/* Sign Out Icon */}
                  <button
                    onClick={handleSignOut}
                    className="cursor-pointer p-2 rounded-full text-red-500 hover:bg-red-50 transition-colors"
                    title="Sign Out"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                  </button>
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Link 
                  href="/login" 
                  className="text-sm px-3 py-1 rounded-md bg-blue-50 text-blue-600 hover:bg-blue-100 transition-colors"
                >
                  Sign In
                </Link>
                <Link 
                  href="/signup" 
                  className="text-sm px-3 py-1 rounded-md bg-blue-600 text-white hover:bg-blue-700 transition-colors"
                >
                  Sign Up
                </Link>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="md:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 ">
            {session?.user ? (
              <div className="flex flex-col space-y-3 p-3">
                <div className="flex items-center space-x-3">
                  {/* User Avatar */}
                  {userImage ? (
                    <img
                      src={userImage}
                      alt="Profile"
                      className="h-10 w-10 rounded-full object-cover border border-gray-200"
                    />
                  ) : (
                    <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center border border-gray-200">
                      <span className="text-lg font-medium text-blue-600">
                        {userInitial}
                      </span>
                    </div>
                  )}
                  
                  {/* User Info - Mobile */}
                  <div className="flex flex-col">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-gray-700">{userName}</span>
                      <span className="text-xs font-medium px-2 py-0.5 rounded-full bg-blue-100 text-blue-800 inline-block">
                        {userRole}
                      </span>
                    </div>
                    <span className="text-xs text-gray-500">{userEmail}</span>
                  </div>
                </div>
                
                {/* Mobile Action Buttons */}
                <div className="grid grid-cols-3 gap-2 mt-2">
                  <Link 
                    href="/" 
                    className="flex items-center justify-center gap-2 py-2 bg-green-50 text-green-600 rounded-md hover:bg-green-100 transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                    <span>Home</span>
                  </Link>
                  
                  <Link 
                    href="/profile" 
                    className="flex items-center justify-center gap-2 py-2 bg-blue-50 text-blue-600 rounded-md hover:bg-blue-100 transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <span>Profile</span>
                  </Link>
                  
                  <button
                    onClick={handleSignOut}
                    className="cursor-pointer flex items-center justify-center gap-2 py-2 bg-red-50 text-red-500 rounded-md hover:bg-red-100 transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                    <span>Sign Out</span>
                  </button>
                </div>
              </div>
            ) : (
              <div className="flex flex-col space-y-2 p-3">
                <Link 
                  href="/login" 
                  className="block text-center text-sm px-3 py-2 rounded-md bg-blue-50 text-blue-600 hover:bg-blue-100 transition-colors"
                >
                  Sign In
                </Link>
                <Link 
                  href="/signup" 
                  className="block text-center text-sm px-3 py-2 rounded-md bg-blue-600 text-white hover:bg-blue-700 transition-colors"
                >
                  Sign Up
                </Link>
              </div>
            )}
          </div>
        </div>
      )}
    </nav>
  );
}







