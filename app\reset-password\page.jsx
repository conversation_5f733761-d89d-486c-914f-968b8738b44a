'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import Swal from 'sweetalert2';

export default function ResetPasswordPage() {
  const searchParams = useSearchParams();
  const router = useRouter();

  const email = searchParams.get('email') || '';
  const token = searchParams.get('token') || '';

  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [strength, setStrength] = useState(0);

  const [validation, setValidation] = useState({
    length: false,
    uppercase: false,
    lowercase: false,
    number: false,
    special: false,
  });

  useEffect(() => {
    if (!token) {
      Swal.fire({
        icon: 'error',
        title: 'Invalid reset link',
        text: 'Missing token. Please try requesting a new reset link.',
      });
      router.push('/login');
    }
  }, [email, token, router]);

  const checkPasswordStrength = (password) => {
    let score = 0;
    if (password.length >= 8) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[a-z]/.test(password)) score++;
    if (/\d/.test(password)) score++;
    if (/[@$!%*#?&^+=]/.test(password)) score++;
    return score;
  };

  const getStrengthLabel = () => {
    switch (strength) {
      case 0: return 'Very Weak';
      case 1: return 'Weak';
      case 2: return 'Fair';
      case 3: return 'Good';
      case 4: return 'Strong';
      case 5: return 'Very Strong';
      default: return '';
    }
  };

  const handlePasswordChange = (e) => {
    const value = e.target.value;
    setPassword(value);
    setStrength(checkPasswordStrength(value));

    setValidation({
      length: value.length >= 8,
      uppercase: /[A-Z]/.test(value),
      lowercase: /[a-z]/.test(value),
      number: /\d/.test(value),
      special: /[@$!%*#?&^+=]/.test(value),
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (loading) return;

    setMessage('');

    if (password !== confirmPassword) {
      Swal.fire({
        icon: 'error',
        title: 'Passwords do not match',
        text: 'Please make sure both passwords are the same.',
      });
      return;
    }

    if (!Object.values(validation).every(Boolean)) {
      Swal.fire({
        icon: 'warning',
        title: 'Weak Password',
        html: 'Please make sure your password meets all the requirements.',
      });
      return;
    }

    setLoading(true);

    try {
      const res = await fetch('/api/reset-password', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, token, password }),
      });

      const data = await res.json();

      if (res.ok) {
        Swal.fire({
          icon: 'success',
          title: 'Password reset successfully!',
          text: 'Redirecting to login page...',
          timer: 3000,
          showConfirmButton: false,
        });
        setTimeout(() => router.push('/login'), 2000);
      } else {
        Swal.fire({
          icon: 'error',
          title: 'Reset failed',
          text: data.message || 'Failed to reset password. Please try again.',
        });
      }
    } catch (error) {
      console.error(error);
      Swal.fire({
        icon: 'error',
        title: 'Something went wrong',
        text: 'Please try again later.',
      });
    } finally {
      setLoading(false);
      setPassword('');
      setConfirmPassword('');
      setStrength(0);
      setValidation({
        length: false,
        uppercase: false,
        lowercase: false,
        number: false,
        special: false,
      });
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 p-4">
      <div className="w-full max-w-md p-8 bg-white rounded-2xl shadow-xl">
        <h1 className="text-2xl font-bold text-gray-800 text-center">
          Reset Your Password
        </h1>
        <p className="mt-2 mb-8 text-sm text-gray-400 text-center">
          Resetting for: <strong>{email}</strong>
        </p>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-gray-700 mb-1">New Password</label>
            <input
              type="password"
              value={password}
              onChange={handlePasswordChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder:text-gray-300"
              placeholder="Enter new password"
            />
            <div className="mt-1 text-xs text-gray-600">
              Password strength: <b>{getStrengthLabel()}</b>
            </div>
            <div className="mt-1 h-1 w-full bg-gray-200 rounded">
              <div
                className={`h-1 rounded transition-all duration-300
                  ${strength <= 1 ? 'bg-red-400 w-1/5' :
                    strength === 2 ? 'bg-orange-400 w-2/5' :
                      strength === 3 ? 'bg-yellow-400 w-3/5' :
                        strength === 4 ? 'bg-green-400 w-4/5' :
                          'bg-green-600 w-full'}`}
              ></div>
            </div>
            <div className="mt-2 space-y-1 text-sm">
              <div className={validation.length ? 'text-green-600' : 'text-gray-500'}>
                {validation.length ? '✔' : '✖'} At least 8 characters
              </div>
              <div className={validation.uppercase ? 'text-green-600' : 'text-gray-500'}>
                {validation.uppercase ? '✔' : '✖'} Uppercase letter (A-Z)
              </div>
              <div className={validation.lowercase ? 'text-green-600' : 'text-gray-500'}>
                {validation.lowercase ? '✔' : '✖'} Lowercase letter (a-z)
              </div>
              <div className={validation.number ? 'text-green-600' : 'text-gray-500'}>
                {validation.number ? '✔' : '✖'} Number (0-9)
              </div>
              <div className={validation.special ? 'text-green-600' : 'text-gray-500'}>
                {validation.special ? '✔' : '✖'} Special character (@$!%*#?&^+=)
              </div>
            </div>
          </div>

          <div>
            <label className="block text-gray-700 mb-1">Confirm Password</label>
            <input
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder:text-gray-300"
              placeholder="Confirm your password"
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className={`w-full bg-blue-600 text-white py-2 rounded transition cursor-pointer 
              ${loading ? 'opacity-50 cursor-not-allowed hover:bg-gray-500' : 'hover:bg-blue-700'}`}
          >
            {loading ? 'Processing...' : 'Reset Password'}
          </button>

          {message && (
            <div className="mb-4 p-3 rounded bg-yellow-100 text-yellow-800">
              {message}
            </div>
          )}
        </form>


      </div>
    </div>
  );
}
