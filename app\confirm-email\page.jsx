'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import Swal from 'sweetalert2';

export default function ConfirmEmail() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  
  const [status, setStatus] = useState('verifying'); // verifying, success, error

  useEffect(() => {
    if (!token) {
      setStatus('error');
      Swal.fire({
        icon: 'error',
        title: 'Invalid Token',
        text: 'The confirmation link is invalid.',
        confirmButtonColor: '#ef4444'
      });
      return;
    }

    const confirmEmail = async () => {
      try {
        const res = await fetch('/api/confirm-email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ token }),
        });

        const data = await res.json();

        if (res.ok) {
          setStatus('success');
          
          // เก็บข้อมูล ใช้ไว้ใน localStorage เลือกใช้ในหน้า login
          if (data.email && data.profileImage) {
            localStorage.setItem('confirmedUserEmail', data.email);
            localStorage.setItem('confirmedUserProfileImage', data.profileImage);
          }
          
          await Swal.fire({
            icon: 'success',
            title: 'Email Confirmed!',
            text: 'Your account has been activated successfully.',
            confirmButtonColor: '#22c55e'
          });
          router.push('/login');
        } else {
          setStatus('error');
          Swal.fire({
            icon: 'error',
            title: 'Confirmation Failed',
            text: data.message || 'Failed to confirm your email. The link may have expired.',
            confirmButtonColor: '#ef4444'
          });
        }
      } catch (error) {
        console.error('Error confirming email:', error);
        setStatus('error');
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'An error occurred while confirming your email.',
          confirmButtonColor: '#ef4444'
        });
      }
    };

    confirmEmail();
  }, [token, router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-blue-50">
      <div className="bg-white py-8 px-8 rounded-xl shadow-lg w-96 border border-gray-100 text-center">
        <h2 className="text-2xl font-bold mb-6 text-gray-800">Email Confirmation</h2>
        
        {status === 'verifying' && (
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto"></div>
            <p className="mt-4 text-gray-600">Verifying your email...</p>
          </div>
        )}
        
        {status === 'success' && (
          <div>
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">Email Confirmed!</h3>
            <p className="text-gray-600 mb-6">Your account has been activated successfully.</p>
            <Link href="/login" className="inline-block bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-6 rounded transition duration-150">
              Sign In
            </Link>
          </div>
        )}
        
        {status === 'error' && (
          <div>
            <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">Confirmation Failed</h3>
            <p className="text-gray-600 mb-6">The confirmation link is invalid or has expired.</p>
            <div className="flex flex-col space-y-3">
              <Link href="/login" className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded transition duration-150">
                Sign In
              </Link>
              <Link href="/signup" className="inline-block bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-6 rounded transition duration-150">
                Sign Up Again
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
