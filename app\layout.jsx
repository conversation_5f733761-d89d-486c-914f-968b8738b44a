// app/layout.jsx

import { <PERSON><PERSON><PERSON>, <PERSON>san<PERSON>, <PERSON><PERSON>, <PERSON>o_Sans_Thai } from 'next/font/google' 
import "./globals.css";
import ClientSessionProvider from './components/ClientSessionProvider';
import Navbar from './components/Navbar';
import { Suspense } from 'react';
import Loading from './loading';

export const metadata = {
  title: "NextJS-User-Management",
  description: "Generated by Jumlong Ch.",
};

const roboto = Roboto({
  weight: ['400'],
  subsets: ['thai', 'latin'],
  variable: '--font-roboto',
})
const anuphan = Anuphan({
  weight: ['400'],
  subsets: ['thai', 'latin'],
  variable: '--font-anuphan',
})
const quicksand = Quicksand({
  weight: ['500'],
  subsets: ['thai', 'latin'],
  variable: '--font-quicksand',
})
const noto_sand_thai = Noto_Sans_Thai({
  weight: ['400'],
  subsets: ['thai', 'latin'],
  variable: '--font-notosandthai',
})

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${noto_sand_thai.className} tracking-normal antialiased`}
      >
        <ClientSessionProvider>
          <Navbar />
          <Suspense fallback={<Loading />}>
            <main>
              {children}
            </main>
          </Suspense>
        </ClientSessionProvider>
      </body>
    </html>
  );
}

