import { connectToDatabase } from '@/lib/db';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import { NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import path from 'path';

export async function POST(req) {
  try {
    const formData = await req.formData();

    const email = formData.get('email');
    const fullname = formData.get('fullname');
    const password = formData.get('password');
    const profileImageFile = formData.get('profileImage');

    if (!email || !fullname || !password) {
      return new Response(JSON.stringify({ message: 'Missing required fields' }), { status: 400 });
    }

    // ตรวจสอบความยาวของ fullname
    if (fullname.trim().length < 4) {
      return new Response(JSON.stringify({ message: 'Full name must be at least 4 characters' }), { status: 400 });
    }

    const passwordPolicy = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*#?&]).{8,}$/;
    if (!passwordPolicy.test(password)) {
      return new Response(JSON.stringify({ message: 'Password does not meet complexity requirements' }), { status: 400 });
    }

    const pool = await connectToDatabase();

    const existing = await pool.request()
      .input('email', email)
      .query('SELECT * FROM dbo.PMS3_Users WHERE Email = @email');

    if (existing.recordset.length > 0) {
      return new Response(JSON.stringify({ message: 'Email already exists' }), { status: 400 });
    }

    // สร้าง confirmation token
    const confirmationToken = uuidv4();
    const tokenExpiry = new Date();
    tokenExpiry.setHours(tokenExpiry.getHours() + 24); // หมด ใน 24  ชั่วโมง

    // เข้ารหัส
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    //  profile image
    let profileImagePath = null;
    let profileImageContentType = null;

    if (profileImageFile) {
      try {
        // ตรวจสอบขนาดไฟล์ (5MB)
        const maxSize = 5 * 1024 * 1024;
        if (profileImageFile.size > maxSize) {
          return new Response(JSON.stringify({ message: 'Profile image must not exceed 5MB' }), { status: 400 });
        }

        // ตรวจสอบประเภทไฟล์
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(profileImageFile.type)) {
          return new Response(JSON.stringify({ message: 'Only JPEG, PNG, GIF, and WebP images are allowed' }), { status: 400 });
        }

        // สร้างชื่อไฟล์ใหม่
        const fileExtension = path.extname(profileImageFile.name) || '.jpg';
        const fileName = `${uuidv4()}${fileExtension}`;
        
        // สร้างโฟลเดอร์ถ้าไม่
        const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'profiles');
        const filePath = path.join(uploadDir, fileName);
        
        try {
          await mkdir(uploadDir, { recursive: true });
        } catch (error) {
          console.log('Directory already exists or creation failed:', error);
        }
        
        // บันทึกไฟล์
        const arrayBuffer = await profileImageFile.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        
        await writeFile(filePath, buffer);
        
        // เก็บข้อมูลไฟล์
        profileImagePath = `/uploads/profiles/${fileName}`;
        profileImageContentType = profileImageFile.type;
      } catch (error) {
        console.error('Error processing profile image:', error);
        return new Response(JSON.stringify({ message: 'Error processing profile image' }), { status: 500 });
      }
    }

    // Insert user ลง DB โดยตั้งค่า Active = 0 และมี confirmation token
    await pool.request()
      .input('email', email)
      .input('password', hashedPassword)
      .input('fullname', fullname)
      .input('role', 'User')
      .input('profileImagePath', profileImagePath)
      .input('profileImageContentType', profileImageContentType)
      .input('active', 0) // ตั้งค่า Active = 0
      .input('confirmationToken', confirmationToken)
      .input('confirmationTokenExpiry', tokenExpiry)
      .query(`
        INSERT INTO dbo.PMS3_Users 
          (Email, Password, FullName, Role, ProfileImagePath, ProfileImageContentType, 
           Active, ResetToken, ResetTokenExpire, CreatedDate)
        VALUES 
          (@email, @password, @fullname, @role, @profileImagePath, @profileImageContentType, 
           @active, @confirmationToken, @confirmationTokenExpiry, GETDATE())
      `);

    // Password History
    await pool.request()
      .input('email', email)
      .input('password', hashedPassword)
      .query(`
        INSERT INTO dbo.PMS3_UserPasswordHistory (Email, PasswordHash, CreatedAt)
        VALUES (@email, @password, GETDATE())
      `);

    // ส่งอีเมลยืนยัน
    try {
      const confirmUrl = `${process.env.NEXT_PUBLIC_APP_URL}/confirm-email?token=${confirmationToken}`;
      
      // เรียกใช้ API ส่งอีเมล
      await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/send-confirmation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          name: fullname,
          confirmationToken,
          confirmUrl
        }),
      });
    } catch (emailError) {
      console.error('Error sending confirmation email:', emailError);
      // ไม่ต้อง return error เพราะการสมัครสำเร็จแล้ว แค่ส่งอีเมลไม่สำเร็จ
    }

    return new Response(JSON.stringify({ 
      message: 'User created successfully. Please check your email to confirm your account.' 
    }), { status: 201 });

  } catch (error) {
    console.error('Signup error:', error);
    return new Response(JSON.stringify({ message: 'Registration error' }), { status: 500 });
  }
}
