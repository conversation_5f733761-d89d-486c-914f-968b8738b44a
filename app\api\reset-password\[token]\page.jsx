'use client';
import { useState } from 'react';

export default function ResetPasswordPage({ params }) {
    const { token } = params;
    const [password, setPassword] = useState('');

    const handleChangePassword = async () => {
        const res = await fetch('/api/change-password', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ token, password }),
        });

        const data = await res.json();
        alert(data.message);
    };

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-100">
            <div className="bg-white p-8 rounded-lg shadow-md w-96"></div>
            <h1 className="text-2xl font-bold text-gray-800 mb-6 text-center">ตั้งรหัสผ่านใหม่</h1>
            <div className="space-y-4">
                <input
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full border rounded-md p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 transition"
                    placeholder="รหัสผ่านใหม่"
                />
                <button
                    onClick={handleChangePassword}
                    className="w-full bg-blue-500 text-white py-3 rounded-md hover:bg-blue-600 transition duration-200 font-medium"
                >
                    เปลี่ยนรหัสผ่าน
                </button>
            </div>
        </div>
    );
}
