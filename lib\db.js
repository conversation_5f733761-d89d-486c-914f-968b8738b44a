import sql from 'mssql';

const config = {
  user: 'sa',
  password: 'sql@<PERSON>',
  server: 'MILDB_SQL2019',
  database: 'EPIC_PMS',
  options: {
    encrypt: false,
    trustServerCertificate: true
  }
};

let pool;

export async function connectToDatabase() {
  try {
    const pool = await sql.connect(config);
    return pool;
  } catch (err) {
    console.error("Database connection failed:", err);
    throw err;
  }
}

export { sql };