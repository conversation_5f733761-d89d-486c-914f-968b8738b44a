import { connectToDatabase } from '@/lib/db';
import { NextResponse } from 'next/server';

export async function POST(req) {
  try {
    const { token } = await req.json();

    if (!token) {
      return NextResponse.json({ message: 'Token is required' }, { status: 400 });
    }

    const pool = await connectToDatabase();

    // ตรวจสอบ token และอัปเดตสถานะ Active
    const result = await pool.request()
      .input('token', token)
      .query(`
        UPDATE dbo.PMS3_Users
        SET Active = 1, ResetToken = NULL, ResetTokenExpire = NULL
        OUTPUT INSERTED.Email, INSERTED.FullName, INSERTED.ProfileImagePath
        WHERE ResetToken = @token AND ResetTokenExpire > GETDATE()
      `);

    if (result.recordset.length === 0) {
      return NextResponse.json(
        { message: 'Invalid or expired confirmation token' },
        { status: 400 }
      );
    }

    const user = result.recordset[0];

    return NextResponse.json({
      message: '<PERSON><PERSON> confirmed successfully',
      email: user.Email,
      name: user.FullName,
      profileImage: user.ProfileImagePath
    });
  } catch (error) {
    console.error('Error confirming email:', error);
    return NextResponse.json(
      { message: 'Failed to confirm email' },
      { status: 500 }
    );
  }
}
