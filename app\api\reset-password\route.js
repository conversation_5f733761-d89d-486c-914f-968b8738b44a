import { NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/db";
import bcrypt from "bcrypt";

// Function เช็คความแข็งแรงรหัสผ่าน
function isPasswordStrong(password) {
  const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*#?&^+=])[A-Za-z\d@$!%*#?&^+=]{8,}$/;
  return regex.test(password);
}

export async function POST(req) {
  try {
    const { token, password } = await req.json();

    if (!token || !password) {
      return NextResponse.json(
        { message: "Token and password are required." },
        { status: 400 }
      );
    }

    if (!isPasswordStrong(password)) {
      return NextResponse.json(
        {
          message:
            "Password must be at least 8 characters long and include uppercase, lowercase, number, and special character.",
        },
        { status: 400 }
      );
    }

    const pool = await connectToDatabase();

    // หา user จาก token ตรงๆ
    const userResult = await pool
      .request()
      .input("token", token)
      .query(`
        SELECT Id, Email, ResetTokenExpire
        FROM dbo.PMS3_Users
        WHERE ResetToken = @token AND Active=1
      `);

    if (userResult.recordset.length === 0) {
      return NextResponse.json(
        { message: "Invalid or expired token." },
        { status: 400 }
      );
    }

    const user = userResult.recordset[0];

    if (!user.ResetTokenExpire || new Date(user.ResetTokenExpire) < new Date()) {
      return NextResponse.json(
        { message: "Token has expired." },
        { status: 400 }
      );
    }

    // ดึง password history
    const historyResult = await pool
      .request()
      .input("email", user.Email)
      .query(`
        SELECT PasswordHash
        FROM dbo.PMS3_UserPasswordHistory
        WHERE Email = @email
        ORDER BY CreatedAt DESC
      `);

    const previousHashes = historyResult.recordset.map((r) => r.PasswordHash);

    for (let hash of previousHashes) {
      const isSame = await bcrypt.compare(password, hash);
      if (isSame) {
        return NextResponse.json(
          {
            message: "New password must not match any previously used passwords.",
          },
          { status: 400 }
        );
      }
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    // บันทึก password history ใหม่
    await pool
      .request()
      .input("email", user.Email)
      .input("passwordHash", hashedPassword)
      .input("createdAt", new Date())
      .query(`
        INSERT INTO dbo.PMS3_UserPasswordHistory (Email, PasswordHash, CreatedAt)
        VALUES (@email, @passwordHash, @createdAt)
      `);

    // อัปเดต password และเคลียร์ token
    await pool
      .request()
      .input("userId", user.Id)
      .input("password", hashedPassword)
      .query(`
        UPDATE dbo.PMS3_Users
        SET Password = @password, ResetToken = NULL, ResetTokenExpire = NULL, UpdatedDate = GETDATE()
        WHERE Id = @userId
      `);

    return NextResponse.json({ message: "Password reset successfully." });

  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { message: "Something went wrong." },
      { status: 500 }
    );
  }
}
