import { connectToDatabase } from '@/lib/db';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import bcrypt from 'bcryptjs';
import { writeFile, mkdir } from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

export async function POST(req) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return new Response(JSON.stringify({ message: 'Unauthorized' }), { status: 401 });
    }

    const formData = await req.formData();
    const fullname = formData.get('fullname');
    const currentPassword = formData.get('currentPassword');
    const newPassword = formData.get('newPassword');
    const profileImageFile = formData.get('profileImage');

    if (!fullname) {
      return new Response(JSON.stringify({ message: 'Full name is required' }), { status: 400 });
    }

    const pool = await connectToDatabase();
    
    // Get current user data
    const userResult = await pool.request()
      .input('email', session.user.email)
      .query('SELECT * FROM dbo.PMS3_Users WHERE Email = @email');
    
    if (userResult.recordset.length === 0) {
      return new Response(JSON.stringify({ message: 'User not found' }), { status: 404 });
    }
    
    const user = userResult.recordset[0];
    
    // Check current password if trying to change password
    if (newPassword && !currentPassword) {
      return new Response(JSON.stringify({ message: 'Current password is required to set a new password' }), { status: 400 });
    }
    
    if (currentPassword) {
      const isPasswordValid = await bcrypt.compare(currentPassword, user.Password);
      if (!isPasswordValid) {
        return new Response(JSON.stringify({ message: 'Current password is incorrect' }), { status: 400 });
      }
    }
    
    // Handle password change
    let hashedPassword = null;
    if (newPassword) {
      // Check password history
      const historyResult = await pool.request()
        .input('email', session.user.email)
        .query(`
          SELECT PasswordHash
          FROM dbo.PMS3_UserPasswordHistory
          WHERE Email = @email
          ORDER BY CreatedAt DESC
        `);
      
      // Check if new password matches any previous passwords
      for (let record of historyResult.recordset) {
        const isSame = await bcrypt.compare(newPassword, record.PasswordHash);
        if (isSame) {
          return new Response(JSON.stringify({ message: 'New password cannot match previously used passwords' }), { status: 400 });
        }
      }
      
      hashedPassword = await bcrypt.hash(newPassword, 10);
    }
    
    // Handle profile image
    let profileImagePath = user.ProfileImagePath;
    let profileImageContentType = user.ProfileImageContentType;
    
    if (profileImageFile && profileImageFile.size > 0) {
      // Check file size (5MB max)
      const maxSize = 5 * 1024 * 1024;
      if (profileImageFile.size > maxSize) {
        return new Response(JSON.stringify({ message: 'Profile image size must not exceed 5MB' }), { status: 400 });
      }
      
      // Check file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(profileImageFile.type)) {
        return new Response(JSON.stringify({ message: 'Invalid image format. Only JPEG, PNG, GIF, and WebP are allowed' }), { status: 400 });
      }
      
      profileImageContentType = profileImageFile.type;
      
      // Create new filename
      const fileExtension = path.extname(profileImageFile.name) || '.jpg';
      const fileName = `${uuidv4()}${fileExtension}`;
      
      // Create directory if it doesn't exist
      const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'profiles');
      const filePath = path.join(uploadDir, fileName);
      
      try {
        await mkdir(uploadDir, { recursive: true });
      } catch (error) {
        console.log('Directory already exists or creation failed:', error);
      }
      
      // Save the file
      const arrayBuffer = await profileImageFile.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      
      await writeFile(filePath, buffer);
      
      // Store the relative path
      profileImagePath = `/uploads/profiles/${fileName}`;
    }
    
    // Update user profile
    const updateQuery = `
      UPDATE dbo.PMS3_Users
      SET FullName = @fullname,
          ${hashedPassword ? 'Password = @password,' : ''}
          ${profileImagePath !== user.ProfileImagePath ? 'ProfileImagePath = @profileImagePath, ProfileImageContentType = @profileImageContentType,' : ''}
          UpdatedDate = GETDATE()
      WHERE Email = @email
    `;
    
    const request = pool.request()
      .input('fullname', fullname)
      .input('email', session.user.email);
    
    if (hashedPassword) {
      request.input('password', hashedPassword);
    }
    
    if (profileImagePath !== user.ProfileImagePath) {
      request.input('profileImagePath', profileImagePath);
      request.input('profileImageContentType', profileImageContentType);
    }
    
    await request.query(updateQuery);
    
    // Add new password to history if changed
    if (hashedPassword) {
      await pool.request()
        .input('email', session.user.email)
        .input('passwordHash', hashedPassword)
        .query(`
          INSERT INTO dbo.PMS3_UserPasswordHistory (Email, PasswordHash, CreatedAt)
          VALUES (@email, @passwordHash, GETDATE())
        `);
    }
    
    return new Response(JSON.stringify({ 
      message: 'Profile updated successfully',
      user: {
        name: fullname,
        email: session.user.email,
        image: profileImagePath
      }
    }), { status: 200 });
    
  } catch (error) {
    console.error('Profile update error:', error);
    return new Response(JSON.stringify({ message: 'Error updating profile' }), { status: 500 });
  }
}