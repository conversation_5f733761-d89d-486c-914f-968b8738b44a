'use client';

import { signIn } from 'next-auth/react';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Swal from 'sweetalert2';

export default function LoginPage() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const confirmedEmail = localStorage.getItem('confirmedUserEmail');
    if (confirmedEmail) {
      setEmail(confirmedEmail);
      // ลบข้อมูลใช้งาน
      localStorage.removeItem('confirmedUserEmail');
      localStorage.removeItem('confirmedUserProfileImage');
    }
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    const res = await signIn('credentials', {
      redirect: false,
      email,
      password,
    });

    if (res?.error) {
      // ตรวจสอบว่าเป็นข้อผิดพลาดเกี่ยวกับการยืนยันหรือการเปิดใช้งาน
      if (res.error.includes('not confirmed') || res.error.includes('not activated')) {
        Swal.fire({
          icon: 'warning',
          title: 'Account Not Activated',
          html: `
            <p>Your account has not been activated yet.</p>
            <p class="mt-2">Please check your email and click the confirmation link.</p>
            <p class="mt-2">Need a new confirmation email?</p>
          `,
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Resend Confirmation',
          cancelButtonText: 'Close'
        }).then(async (result) => {
          if (result.isConfirmed) {
            // ส่งคำขอเพื่อส่งอีเมลยืนยันใหม่
            try {
              const resendRes = await fetch('/api/resend-confirmation', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email }),
              });
              
              const resendData = await resendRes.json();
              
              if (resendRes.ok) {
                Swal.fire({
                  icon: 'success',
                  title: 'Email Sent',
                  text: 'A new confirmation email has been sent. Please check your inbox.',
                  confirmButtonColor: '#3085d6',
                });
              } else {
                Swal.fire({
                  icon: 'error',
                  title: 'Failed to Resend',
                  text: resendData.message || 'Failed to send confirmation email.',
                  confirmButtonColor: '#d33',
                });
              }
            } catch (error) {
              Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'An error occurred while sending the confirmation email.',
                confirmButtonColor: '#d33',
              });
            }
          }
        });
      } else {
        // แสดง SweetAlert2 ข้อผิดพลาดอื่นๆ
        Swal.fire({
          icon: 'error',
          title: 'Login Failed',
          text: res.error,
        });
      }
    } else {
      Swal.fire({
        icon: 'success',
        title: 'Login Successful!',
        timer: 1500,
        showConfirmButton: false,
      }).then(() => {
        router.push('/');
      });
    }

    setIsSubmitting(false);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-md w-96">
        <h2 className="text-2xl font-bold mb-6 text-center text-gray-800">Sign in</h2>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Email</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 duration-150 ease-in-out placeholder:text-gray-300"
              placeholder="Enter your email"
              required
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Password</label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 duration-150 ease-in-out placeholder:text-gray-300"
                placeholder="Enter your password"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
              >
                {showPassword ? (
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.242 4.242L9.88 9.88" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                    <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                )}
              </button>
            </div>
          </div>

          <button
            type="submit"
            disabled={isSubmitting}
            className={`w-full py-2 px-4 font-medium rounded-md transition duration-150 ease-in-out cursor-pointer ${isSubmitting
                ? 'bg-gray-400 text-white cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
          >
            {isSubmitting ? 'Logging in...' : 'Login'}
          </button>
        </form>

        <div className="flex justify-between items-center mt-4 text-sm">
          <Link href="/forgot-password" className="text-sm text-gray-400 hover:text-orange-600 transition duration-150 ease-in-out">
            Forgot your password?
          </Link>
          <Link href="/signup" className="text-gray-400  hover:text-orange-600 transition duration-150 ease-in-out">Sign up</Link>
        </div>
      </div>
    </div>
  );
}
