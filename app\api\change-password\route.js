import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/db';
import { hashPassword } from '@/lib/utils';

export async function POST(req) {
  try {
    const { token, password } = await req.json();

    if (!token || !password) {
      return NextResponse.json({ message: 'ข้อมูลไม่ครบ' }, { status: 400 });
    }

    const pool = await connectToDatabase();

    const result = await pool.request()
      .input('token', token)
      .query(`
        SELECT * FROM dbo.PMS3_Users 
        WHERE ResetToken = @token AND ResetTokenExpire > GETDATE()
      `);

    if (result.recordset.length === 0) {
      return NextResponse.json({ message: 'ลิงก์หมดอายุหรือไม่ถูกต้อง' }, { status: 400 });
    }

    const hashed = await hashPassword(password);

    await pool.request()
      .input('hashedPassword', hashed)
      .input('token', token)
      .query(`
        UPDATE dbo.PMS3_Users 
        SET Password = @hashedPassword, ResetToken = NULL, ResetTokenExpire = NULL
        WHERE ResetToken = @token
      `);

    return NextResponse.json({ message: 'เปลี่ยนรหัสผ่านเรียบร้อยแล้ว' });
  } catch (error) {
    console.error(error);
    return NextResponse.json({ message: 'เกิดข้อผิดพลาด' }, { status: 500 });
  }
}
