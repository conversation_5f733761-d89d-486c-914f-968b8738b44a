import { connectToDatabase } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

export async function GET() {
  try {
    // ตรวจสอบการเข้า<|im_start|>ี่
    const session = await getServerSession(authOptions);
    if (!session) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // เชื่อมต่อกับฐานข้อมูล SQL Server
    const pool = await connectToDatabase();
    
    const result = await pool.request()
      .query(`
        SELECT 
          Id,
          Email,
          FullName,
          Role,
          ProfileImagePath,
          ProfileImageContentType,
          Active,
          CreatedDate,
          UpdatedDate
        FROM dbo.PMS3_Users
        ORDER BY CreatedDate DESC
      `);

    // แปลงข้อมูลให้เหมาะสม<|im_start|>ใช้งานในหน้า UI
    const users = result.recordset.map(user => {
      console.log(`User ${user.FullName} active from DB:`, user.Active, typeof user.Active);
      
      return {
        _id: user.Id,
        email: user.Email,
        fullName: user.FullName,
        role: user.Role,
        profileImagePath: user.ProfileImagePath,
        profileImageContentType: user.ProfileImageContentType,
        active: user.Active === true, // แปลงเป็น boolean อย่าง
        createdAt: user.CreatedDate,
        updatedAt: user.UpdatedDate
      };
    });

    return Response.json({ users });
  } catch (error) {
    console.error('Error fetching users:', error);
    return Response.json({ error: 'Failed to fetch users' }, { status: 500 });
  }
}

