'use client';

import Image from "next/image";
import { useSession, signIn, signOut } from "next-auth/react";
import { useEffect } from "react";

export default function Home() {
  const { data: session, status } = useSession();

  useEffect(() => {
    if (status === 'unauthenticated') {
      //signIn(); // Redirect ไปหน้า login
    }
  }, [status]);

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="p-8 rounded-lg">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500">
              {/* <p className="text-gray-600 text-lg mt-4">Loading...</p> */}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (status === 'authenticated') {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="p-8 rounded-xl shadow-2xl bg-white max-w-md w-full mx-4">
          <h1 className="text-3xl font-bold mb-4 text-center text-gray-800">
            Welcome ... {session.user.email}
          </h1>
          <p className="text-gray-600 text-center text-lg">
            You are successfully logged in.
          </p>
          <div className="mt-6 flex gap-4 justify-center">
            {/* <button onClick={() => signOut()}
              className="w-full py-2 bg-blue-600 text-white rounded hover:bg-glue-700 cursor-pointer transition">Sign Out</button>
            <button onClick={() => window.location.href = '/profile'}
              className="w-full py-2 bg-blue-600 text-white rounded hover:bg-blue-700 cursor-pointer transition">
              Go to Profile
            </button> */}
            <button onClick={() => window.location.href = '/users'}
              className="w-full py-2 bg-green-600 text-white rounded hover:bg-green-700 cursor-pointer transition">
              View All Users
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="p-8 rounded-xl shadow-2xl bg-white max-w-md w-full mx-4">
          <h1 className="text-2xl font-bold mb-4 text-center text-gray-800">
            <span className="text-red-500">You are not authenticated.</span>
          </h1>
          <button
            onClick={() => signIn()}
            className="w-full py-2 px-4 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition duration-200 cursor-pointer"
          >
            Sign In
          </button>
        </div>
      </div>
    );
  }

  // ระหว่างรอสถานะ หรือถูก redirect อยู่
  return null;
}

