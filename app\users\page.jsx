'use client';

import { useState, useEffect, useMemo } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Swal from 'sweetalert2';
import { FaEdit, FaTrashAlt, FaUserPlus, FaSortUp, FaSortDown, FaSort, FaCheckCircle, FaTimesCircle, FaSave } from 'react-icons/fa';
import { BiArrowBack } from 'react-icons/bi';
import { IoMdAdd } from 'react-icons/io';
import { MdSort } from 'react-icons/md';

export default function UsersPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [users, setUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState('fullName');
  const [sortDirection, setSortDirection] = useState('asc');

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Selected user state
  const [selectedUser, setSelectedUser] = useState(null);
  const [editedUser, setEditedUser] = useState(null);
  console.log('Initial state - selectedUser:', selectedUser, 'editedUser:', editedUser);
  const [isSaving, setIsSaving] = useState(false);

  // Function to handle user deletion
  const handleDeleteUser = async (userId) => {
    try {
      // Show confirmation dialog
      const result = await Swal.fire({
        title: 'Are you sure?',
        text: "You won't be able to revert this!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Yes, delete it!'
      });

      if (result.isConfirmed) {
        const response = await fetch(`/api/users/${userId}`, {
          method: 'DELETE',
        });

        if (response.ok) {
          // Remove user from state
          setUsers(users.filter(user => user._id !== userId));
          
          // If the deleted user was selected, clear the selection
          if (selectedUser?._id === userId) {
            setSelectedUser(null);
            setEditedUser(null);
          }

          Swal.fire(
            'Deleted!',
            'User has been deleted.',
            'success'
          );
        } else {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Cannot delete this user. The user might have associated data or permissions that prevent deletion.');
        }
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      Swal.fire({
        icon: 'error',
        title: 'Cannot Delete User',
        text: error.message || 'This user cannot be deleted. They may have active assignments or permissions that need to be removed first.',
        confirmButtonColor: '#ef4444'
      });
    }
  };

  // ข้อมูลผู้ใช้หมด
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/users/getAll');
        if (response.ok) {
          const data = await response.json();
          console.log('Fetched users:', data.users);

          // ตรวจสอบค่า active ของแต่ละ user
          data.users.forEach(user => {
            console.log(`User ${user.fullName} active status:`, user.active);
          });

          // เก็บข้อมูล users
          setUsers(data.users);

          // ไม่ต้อง user แรกเพราะเราจะเลือกแล้ว
        } else {
          console.error('Failed to fetch users');
        }
      } catch (error) {
        console.error('Error fetching users:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (status === 'authenticated') {
      fetchUsers();
    } else if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // เรื่อง useEffect ใหม่เพื่อ user คนแรกแล้ว
  useEffect(() => {
    // ทำงานเมื่อข้อมูล users และไม่ได้โหลด
    if (users.length > 0 && !isLoading && !selectedUser) {
      console.log('Sorting and selecting first user...');

      // เรียงลำ ข้อมูลตาม sortField และ sortDirection
      const sortedUsers = [...users].sort((a, b) => {
        let aValue = a[sortField] || '';
        let bValue = b[sortField] || '';

        if (typeof aValue === 'string') aValue = aValue.toLowerCase();
        if (typeof bValue === 'string') bValue = bValue.toLowerCase();

        if (sortDirection === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });
      
      // เลือก user คนแรกแล้ว
      const firstUser = sortedUsers[0];
      if (firstUser) {
        console.log('Auto-selecting first user:', firstUser);
        
        // สร้าง object ใหม่และกำหนดค่า active อย่างต้อง
        const editedUserData = {
          ...firstUser,
          active: Boolean(firstUser.active)
        };
        
        setSelectedUser(firstUser);
        setEditedUser(editedUserData);
      }
    }
  }, [users, isLoading]); // ลบ sortField, sortDirection, selectedUser ออกจาก dependencies

  // ก์งลำข้อมูลผู้ใช้
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
    
    // ใช้ setTimeout เอ่อให้การงลำเสร็จก่อน แล้วค่อย user ใหม่
    setTimeout(() => {
      // ถ้า user ่้อยู่แล้ว ให้หา user ่้ในรายการงใหม่
      if (selectedUser) { // แก้จาก currentSelectedUser เป็น selectedUser
        const sortedUsers = [...users].sort((a, b) => {
          let aValue = a[field] || '';
          let bValue = b[field] || '';
          
          if (typeof aValue === 'string') aValue = aValue.toLowerCase();
          if (typeof bValue === 'string') bValue = bValue.toLowerCase();
          
          if ((sortField === field && sortDirection === 'asc') || 
              (sortField !== field && sortDirection === 'desc')) {
            return aValue > bValue ? 1 : -1;
          } else {
            return aValue < bValue ? 1 : -1;
          }
        });
        
        // หา user ่้ในรายการงใหม่
        const userInSortedList = sortedUsers.find(u => u._id === selectedUser._id); // แก้จาก currentSelectedUser เป็น selectedUser
        
        if (userInSortedList) {
          // ถ้าพบ user ่้ในรายการงใหม่ ให้ user ่้
          setSelectedUser(userInSortedList);
          setEditedUser({
            ...userInSortedList,
            active: Boolean(userInSortedList.active)
          });
        } else {
          // ถ้าไม่พบ user ่้ในรายการงใหม่ ให้ user แรก
          const firstUser = sortedUsers[0];
          if (firstUser) {
            setSelectedUser(firstUser);
            setEditedUser({
              ...firstUser,
              active: Boolean(firstUser.active)
            });
          }
        }
      }
    }, 0);
  };

  // 3ก์ 3ข้อมูลใช้ 3้ใช้เพื่อแก้ไข
  const handleSelectUser = (user) => {
    console.log('Selected user:', user);
    console.log('User active value:', user.active, typeof user.active);

    // สร้าง object ใหม่และกำหนดค่า active อย่างต้อง
    const editedUserData = {
      ...user,
      active: Boolean(user.active) // แปลงเป็น boolean อย่าง
    };

    console.log('Edited user data:', editedUserData);

    setSelectedUser(user);
    setEditedUser(editedUserData);
  };

  // 3ก์ 3ข้อมูลฎ้ใช้ 3้ใช้เพื่อแก้ไข
  const handleSaveUser = async () => {
    if (!editedUser) {
      console.error('No user data to save');
      return;
    }

    // ตรวจสอบความยาวของ Full Name
    if (!editedUser.fullName || editedUser.fullName.trim().length <= 4) {
      Swal.fire({
        icon: 'error',
        title: 'Invalid Full Name',
        text: 'Full name must be longer than 4 characters',
        confirmButtonColor: '#ef4444'
      });
      return;
    }

    console.log('Saving user:', editedUser);
    setIsSaving(true);

    try {
      // สร้างข้อมูลจะส่ง API
      const userData = {
        fullName: editedUser.fullName,
        role: editedUser.role,
        active: Boolean(editedUser.active) // แปลงเป็น boolean อย่าง
      };

      console.log('Sending data to API:', userData);

      const response = await fetch(`/api/users/${editedUser._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      // แสดงข้อมูล response หมดเพื่อการตรวจสอบ
      console.log('Response status:', response.status);

      const responseData = await response.json();
      console.log('Response data:', responseData);

      if (response.ok) {
        // ปเดตข้อมูลในรายการด้วยข้อมูลต้อง
        const updatedUser = {
          ...editedUser,
          active: Boolean(editedUser.active)
        };

        setUsers(users.map(user =>
          user._id === editedUser._id ? updatedUser : user
        ));

        // ปเดตข้อมูล
        setSelectedUser(updatedUser);
        setEditedUser(updatedUser);

        Swal.fire({
          icon: 'success',
          title: 'Success',
          text: 'User updated successfully',
        });
      } else {
        throw new Error(responseData.error || responseData.message || 'Failed to update user');
      }
    } catch (error) {
      console.error('Error saving user:', error);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: error.message || 'Failed to update user',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // 3ก์ 3ยก 3การแก้ไข
  const handleCancelEdit = () => {
    setEditedUser({ ...selectedUser });
  };

  // กรองและ ยกข้อมูล - ย้ายมาไว้ก่อน ่จะ ใช้
  const filteredAndSortedUsers = useMemo(() => {
    return users
      .filter(user => {
        // กรองตามคำค้นหา
        const searchLower = searchTerm.toLowerCase();
        return (
          user.fullName?.toLowerCase().includes(searchLower) ||
          user.email?.toLowerCase().includes(searchLower)
        );
      })
      .sort((a, b) => {
        let aValue = a[sortField] || '';
        let bValue = b[sortField] || '';
        
        if (typeof aValue === 'string') aValue = aValue.toLowerCase();
        if (typeof bValue === 'string') bValue = bValue.toLowerCase();
        
        if (sortDirection === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });
  }, [users, searchTerm, sortField, sortDirection]);

  // Pagination logic
  const currentItems = useMemo(() => {
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    return filteredAndSortedUsers.slice(indexOfFirstItem, indexOfLastItem);
  }, [filteredAndSortedUsers, currentPage, itemsPerPage]);

  // Change page
  const paginate = (pageNumber) => {
    if (pageNumber > 0 && pageNumber <= Math.ceil(filteredAndSortedUsers.length / itemsPerPage)) {
      setCurrentPage(pageNumber);
    }
  };

  // Generate page numbers
  const pageNumbers = [];
  for (let i = 1; i <= Math.ceil(filteredAndSortedUsers.length / itemsPerPage); i++) {
    pageNumbers.push(i);
  }

  // กำหนดค่า uniquePageNumbers ใช้ในการแสดงปุ่ม pagination
  const uniquePageNumbers = pageNumbers.filter((number, index, self) => {
    return self.indexOf(number) === index;
  });

  // แสดงหน้า Loading
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-5xl mx-auto">
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          </div>
        </div>
      </div>
    );
  }



  // แสดงรายการข้อมูลผู้ใช้
  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <div className="container mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-8xl mx-auto">
          <div className="flex flex-col lg:flex-row gap-4">

            {/* ส่วนซ้าย - ตารางข้อมูลผู้้ใช้ */}
            <div className="lg:w-2/3">
              <div className="bg-white rounded-xl shadow-md overflow-hidden">

                {/* ส่วนข้อมูลผู้ใช้ */}
                <div className="px-6 py-5 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                  <h1 className="text-2xl font-bold text-gray-800">User Management</h1>
                  <p className="text-sm text-gray-500">
                    View and manage all users in the system
                  </p>
                </div>

                {/* ส่วนค้นหาและกรอง */}
                <div className="p-4 bg-gray-50 border-b border-gray-200">
                  <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
                    <div className="w-full sm:w-64">
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                          </svg>
                        </div>
                        <input
                          type="text"
                          placeholder="Search users..."
                          className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition"
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                        />
                      </div>
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <span>{filteredAndSortedUsers.length} users found</span>
                    </div>
                  </div>
                </div>

                {/* ตารางแสดงข้อมูล */}
                <div className="relative overflow-hidden">
                  <div className="overflow-x-auto">
                    <div className="overflow-y-auto max-h-[500px]">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-100 sticky top-0 z-10">
                          <tr>
                            <th
                              scope="col"
                              className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200"
                              onClick={() => handleSort('fullName')}
                            >
                              <div className="flex items-center">
                                <span>Name</span>
                                <span className="ml-1">
                                  {sortField === 'fullName' ? (
                                    sortDirection === 'asc' ? <FaSortUp className="inline" /> : <FaSortDown className="inline" />
                                  ) : (
                                    <FaSort className="inline text-gray-400" />
                                  )}
                                </span>
                              </div>
                            </th>
                            <th
                              scope="col"
                              className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200"
                              onClick={() => handleSort('email')}
                            >
                              <div className="flex items-center">
                                <span>Email</span>
                                <span className="ml-1">
                                  {sortField === 'email' ? (
                                    sortDirection === 'asc' ? <FaSortUp className="inline" /> : <FaSortDown className="inline" />
                                  ) : (
                                    <FaSort className="inline text-gray-400" />
                                  )}
                                </span>
                              </div>
                            </th>
                            <th
                              scope="col"
                              className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200"
                              onClick={() => handleSort('role')}
                            >
                              <div className="flex items-center">
                                <span>Role</span>
                                <span className="ml-1">
                                  {sortField === 'role' ? (
                                    sortDirection === 'asc' ? <FaSortUp className="inline" /> : <FaSortDown className="inline" />
                                  ) : (
                                    <FaSort className="inline text-gray-400" />
                                  )}
                                </span>
                              </div>
                            </th>
                            <th
                              scope="col"
                              className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200"
                              onClick={() => handleSort('active')}
                            >
                              <div className="flex items-center">
                                <span>Status</span>
                                <span className="ml-1">
                                  {sortField === 'active' ? (
                                    sortDirection === 'asc' ? <FaSortUp className="inline" /> : <FaSortDown className="inline" />
                                  ) : (
                                    <FaSort className="inline text-gray-400" />
                                  )}
                                </span>
                              </div>
                            </th>
                            <th scope="col" className="px-6 py-3 text-right text-xs font-bold text-gray-500 uppercase tracking-wider bg-gray-100 sticky right-0">
                              Actions
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {currentItems.length > 0 ? (
                            currentItems.map((user, index) => {
                              // ตรวจสอบค่า active ของแต่ละ user ก่อนแสดงผล
                              console.log(`Rendering user ${user.fullName}, active:`, user.active, typeof user.active);
                              return (
                                <tr
                                  key={user._id}
                                  className={`group hover:bg-gray-50 transition-colors ${selectedUser?._id === user._id ? 'bg-blue-50' : ''
                                    }`}
                                  onClick={() => {
                                    console.log('Row clicked, user:', user);
                                    handleSelectUser(user);
                                  }}
                                >
                                  <td className="px-6 py-3 whitespace-nowrap cursor-pointer">
                                    <div className="flex items-center">
                                      <div className="flex-shrink-0 h-8 w-8">
                                        {user.profileImagePath ? (
                                          <img
                                            className="h-8 w-8 rounded-full object-cover border border-gray-200"
                                            src={user.profileImagePath}
                                            alt={user.fullName}
                                          />
                                        ) : (
                                          <div className="h-8 w-8 rounded-full bg-gradient-to-r from-blue-100 to-blue-200 flex items-center justify-center">
                                            <span className="text-blue-600 font-medium">
                                              {user.fullName?.charAt(0) || user.email?.charAt(0) || '?'}
                                            </span>
                                          </div>
                                        )}
                                      </div>
                                      <div className="ml-4">
                                        <div className="text-sm font-medium text-gray-900">{user.fullName}</div>
                                      </div>
                                    </div>
                                  </td>
                                  <td className="px-6 py-3 whitespace-nowrap cursor-pointer">
                                    <div className="text-sm text-gray-500">{user.email}</div>
                                  </td>
                                  <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-500 cursor-pointer">
                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${user.role === 'Admin' ? 'bg-purple-100 text-purple-800' :
                                      user.role === 'Manager' ? 'bg-blue-100 text-blue-800' :
                                        'bg-green-100 text-green-800'
                                      }`}>
                                      {user.role || 'User'}
                                    </span>
                                  </td>
                                  <td className="px-6 py-3 whitespace-nowrap text-sm cursor-pointer">
                                    {console.log(`Rendering status for ${user.fullName}, active:`, user.active, typeof user.active)}
                                    {user.active ? (
                                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <FaCheckCircle className="mr-1" /> Active
                                      </span>
                                    ) : (
                                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <FaTimesCircle className="mr-1" /> Inactive
                                      </span>
                                    )}
                                  </td>
                                  <td className={`px-6 py-3 whitespace-nowrap text-right text-sm font-medium sticky right-0 transition-colors ${selectedUser?._id === user._id ? 'bg-blue-50' : 'bg-white'
                                    } group-hover:bg-gray-50`}>
                                    <button
                                      className="text-blue-600 hover:text-blue-900 inline-flex items-center cursor-pointer p-1 hover:bg-blue-100 rounded-full transition-colors"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleSelectUser(user);
                                      }}
                                      title="Edit user"
                                    >
                                      <FaEdit className="h-4 w-4" />
                                    </button>
                                    {session?.user?.email !== user.email && (
                                      <button
                                        className="text-red-600 hover:text-red-900 inline-flex items-center cursor-pointer p-1 hover:bg-red-100 rounded-full transition-colors ml-1"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleDeleteUser(user._id);
                                        }}
                                        title="Delete user"
                                      >
                                        <FaTrashAlt className="h-4 w-4" />
                                      </button>
                                    )}
                                  </td>
                                </tr>
                              );
                            })
                          ) : (
                            <tr>
                              <td colSpan="5" className="px-6 py-4 text-center text-sm text-gray-500">
                                No users found
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  </div>

                  {/* Pagination */}
                  {filteredAndSortedUsers.length > itemsPerPage && (
                    <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center text-sm text-gray-500">
                          <span>
                            Showing {indexOfFirstItem + 1} to {Math.min(indexOfLastItem, filteredAndSortedUsers.length)} of {filteredAndSortedUsers.length} users
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => paginate(currentPage - 1)}
                            disabled={currentPage === 1}
                            className={`px-3 py-1 rounded-md cursor-pointer ${currentPage === 1
                              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                              : 'bg-white text-blue-600 hover:bg-blue-50 border border-gray-300'
                              }`}
                          >
                            Previous
                          </button>
                          {uniquePageNumbers.map(number => (
                            <button
                              key={number}
                              onClick={() => paginate(number)}
                              className={`px-3 py-1 rounded-md cursor-pointer ${currentPage === number
                                ? 'bg-blue-600 text-white'
                                : 'bg-white text-blue-600 hover:bg-blue-50 border border-gray-300'
                                }`}
                            >
                              {number}
                            </button>
                          ))}
                          <button
                            onClick={() => paginate(currentPage + 1)}
                            disabled={currentPage === Math.ceil(filteredAndSortedUsers.length / itemsPerPage)}
                            className={`px-3 py-1 rounded-md cursor-pointer ${currentPage === Math.ceil(filteredAndSortedUsers.length / itemsPerPage)
                              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                              : 'bg-white text-blue-600 hover:bg-blue-50 border border-gray-300'
                              }`}
                          >
                            Next
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* ส่วนท้าย */}
                <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
                  <div className="flex justify-between items-center">
                    <Link
                      href="/"
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <BiArrowBack className="mr-2 -ml-1 h-5 w-5" />
                      Back to Home
                    </Link>
                    <Link
                      href="/signup"
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <IoMdAdd className="mr-2 -ml-1 h-5 w-5" />
                      Add New User
                    </Link>
                  </div>
                </div>
              </div>
            </div>

            {/* ส่วนแสดงข้อมูล user */}
            {selectedUser && (
              <div className="lg:w-1/3">
                <div className="bg-white rounded-xl shadow-md overflow-hidden">
                  <div className="px-6 py-5 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                    <h2 className="text-xl font-bold text-gray-800">Edit User</h2>
                    <p className="mt-1 text-sm text-gray-500">
                      Update user information
                    </p>
                  </div>

                  <div className="p-6">
                    {/* เข้า console.log เข้าตรวจสอบข้อมูล */}
                    {console.log('EditedUser in render:', editedUser)}

                    <div className="space-y-4">
                      <div>
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                          Email
                        </label>
                        <input
                          type="text"
                          id="email"
                          value={editedUser?.email || ''}
                          disabled
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-100 text-gray-700"
                        />
                      </div>

                      <div>
                        <label htmlFor="fullName" className="block text-sm font-medium text-gray-700">
                          Full Name
                        </label>
                        <input
                          type="text"
                          id="fullName"
                          value={editedUser?.fullName || ''}
                          onChange={(e) => setEditedUser({ ...editedUser, fullName: e.target.value })}
                          className={`mt-1 block w-full px-3 py-2 border ${editedUser?.fullName && editedUser.fullName.trim().length <= 4
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                            : 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500'
                            } rounded-md shadow-sm focus:outline-none`}
                        />
                        {editedUser?.fullName && editedUser.fullName.trim().length <= 4 && (
                          <p className="mt-1 text-sm text-red-600">
                            Full name must be longer than 4 characters
                          </p>
                        )}
                      </div>

                      <div>
                        <label htmlFor="role" className="block text-sm font-medium text-gray-700">
                          Role
                        </label>
                        <select
                          id="role"
                          value={editedUser?.role || ''}
                          onChange={(e) => setEditedUser({ ...editedUser, role: e.target.value })}
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                        >
                          <option value="User">User</option>
                          <option value="Admin">Admin</option>
                        </select>
                      </div>

                      <div>
                        <label className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700">User Status</span>
                          <button
                            type="button"
                            onClick={() => {
                              console.log('Toggle active from:', editedUser.active, 'to:', !editedUser.active);
                              setEditedUser({ ...editedUser, active: !editedUser.active });
                            }}
                            className={`relative inline-flex items-center h-6 rounded-full w-18 transition-colors focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-indigo-500 ${editedUser?.active ? 'bg-green-500' : 'bg-gray-300'
                              }`}
                          >
                            <span
                              className={`inline-block w-4 h-4 transform rounded-full bg-white shadow-md transition-transform duration-100 ${editedUser?.active ? 'translate-x-13' : 'translate-x-1'
                                }`}
                            />
                            <span
                              className={`absolute text-xs font-medium ${editedUser?.active
                                ? 'left-1.5 text-white'
                                : 'right-1.5 text-gray-700'
                                }`}
                            >
                              {editedUser?.active ? 'Active' : 'Inactive'}
                            </span>
                          </button>
                        </label>
                      </div>

                      <div className="flex justify-end space-x-3 pt-4">
                        {/* <button
                        type="button"
                        onClick={handleCancelEdit}
                        className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                      >
                        Cancel
                      </button> */}
                        <button
                          type="button"
                          onClick={handleSaveUser}
                          disabled={isSaving}
                          className="cursor-pointer px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 inline-flex items-center"
                        >
                          {isSaving ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Saving...
                            </>
                          ) : (
                            <>
                              <FaSave className="mr-2 -ml-1 h-4 w-4" />
                              Save Changes
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}






















