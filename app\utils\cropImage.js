const createImage = (url) =>
  new Promise((resolve, reject) => {
    const image = new Image();
    image.addEventListener('load', () => resolve(image));
    image.addEventListener('error', (error) => reject(error));
    image.src = url;
  });

export const getCroppedImg = async (imageSrc, pixelCrop) => {
  const image = await createImage(imageSrc);
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  // สร้าง canvas ขนาดเท่าภาพจะครอป
  canvas.width = pixelCrop.width;
  canvas.height = pixelCrop.height;

  // วาดภาพแล้วลงบน canvas ตามพารามิเตอร์ที่ส่งมาให้ตอนครอปภาพแล้วแปลงเป็น Blob เก็บไว้ในตัวแปร blob แล้วส่งกลับไปให้ตัวแปร resolve รับไปใช้งานต่อไป
  ctx.drawImage(
    image,
    pixelCrop.x,
    pixelCrop.y,
    pixelCrop.width,
    pixelCrop.height,
    0,
    0,
    pixelCrop.width,
    pixelCrop.height
  );

  // แปลง canvas เป็น data URL และแปลงเป็น Blob เก็บไว้ในตัวแปร blob
  return new Promise((resolve) => {
    canvas.toBlob((blob) => {
      // แปลง Blob เป็น URL แล้วส่งกลับไปให้ตัวแปร resolve รับไปใช้งานต่อไป
      resolve(URL.createObjectURL(blob));
    }, 'image/jpeg', 0.95);
  });
};
