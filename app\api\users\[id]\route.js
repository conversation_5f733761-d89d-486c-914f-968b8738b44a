import { connectToDatabase } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import fs from 'fs/promises';
import path from 'path';

// GET user by ID
export async function GET(req, { params }) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.role !== 'Admin') {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;
    const pool = await connectToDatabase();
    
    const result = await pool.request()
      .input('id', id)
      .query(`
        SELECT 
          Id,
          Email,
          FullName,
          Role,
          ProfileImagePath,
          ProfileImageContentType,
          Active,
          CreatedDate,
          UpdatedDate
        FROM dbo.PMS3_Users
        WHERE Id = @id
      `);

    if (result.recordset.length === 0) {
      return Response.json({ error: 'User not found' }, { status: 404 });
    }

    const user = result.recordset[0];
    
    return Response.json({
      user: {
        _id: user.Id,
        email: user.Email,
        fullName: user.FullName,
        role: user.Role,
        profileImagePath: user.ProfileImagePath,
        profileImageContentType: user.ProfileImageContentType,
        active: user.Active === 1,
        createdAt: user.CreatedDate,
        updatedAt: user.UpdatedDate
      }
    });
  } catch (error) {
    console.error('Error fetching user:', error);
    return Response.json({ error: 'Failed to fetch user' }, { status: 500 });
  }
}

// UPDATE user
export async function PUT(req, { params }) {
  try {
    const session = await getServerSession(authOptions);
    
    // แสดงข้อมูล session เลือกตรวจสอบ
    console.log('Session in PUT:', session);
    console.log('User role from session:', session?.user?.role);
    
    if (!session) {
      return Response.json({ error: 'Not authenticated' }, { status: 401 });
    }
    
    // ตรวจสอบ role ของ ้ใช้จากฐานข้อมูล
    const pool = await connectToDatabase();
    const userCheckResult = await pool.request()
      .input('email', session.user.email)
      .query(`
        SELECT Role
        FROM dbo.PMS3_Users
        WHERE Email = @email
      `);
      
    if (userCheckResult.recordset.length === 0) {
      return Response.json({ error: 'User not found in database' }, { status: 404 });
    }
    
    const currentUserRole = userCheckResult.recordset[0].Role;
    console.log('User role from database:', currentUserRole);
    
    // ตรวจสอบ role จากฐานข้อมูล
    if (currentUserRole !== 'Admin') {
      return Response.json({ error: 'Admin permission required' }, { status: 403 });
    }

    const { id } = params;
    console.log('Updating user with ID:', id);
    
    const userData = await req.json();
    console.log('Received user data:', userData);
    console.log('Active value type:', typeof userData.active);
    console.log('Active value:', userData.active);
    
    // แปลงค่า active เป็น 0 หรือ 1 อย่างเหมาะสม
    const activeValue = userData.active === true ? 1 : 0;
    console.log('Converted active value for database:', activeValue);
    
    // ตรวจสอบข้อมูล ่ได้
    if (!userData.fullName || userData.fullName.trim() === '') {
      return Response.json({ error: 'Full name is required' }, { status: 400 });
    }
    
    if (!userData.role) {
      return Response.json({ error: 'Role is required' }, { status: 400 });
    }
    
    // Check if user exists
    const checkResult = await pool.request()
      .input('id', id)
      .query('SELECT Id FROM dbo.PMS3_Users WHERE Id = @id');
    
    if (checkResult.recordset.length === 0) {
      return Response.json({ error: 'User not found' }, { status: 404 });
    }
    
    // Update user
    try {
      await pool.request()
        .input('id', id)
        .input('fullName', userData.fullName)
        .input('role', userData.role)
        .input('active', activeValue) // ใช้ค่าแปลงแล้ว
        .query(`
          UPDATE dbo.PMS3_Users
          SET FullName = @fullName,
              Role = @role,
              Active = @active,
              UpdatedDate = GETDATE()
          WHERE Id = @id
        `);
      
      // ส่งข้อมูล ปเดต
      return Response.json({ 
        message: 'User updated successfully',
        user: {
          _id: id,
          fullName: userData.fullName,
          role: userData.role,
          active: userData.active === true // ส่งเป็น boolean
        }
      });
    } catch (dbError) {
      console.error('Database error:', dbError);
      return Response.json({ error: `Database error: ${dbError.message}` }, { status: 500 });
    }
  } catch (error) {
    console.error('Error updating user:', error);
    return Response.json({ error: `Failed to update user: ${error.message}` }, { status: 500 });
  }
}

// DELETE user
export async function DELETE(req, { params }) {
  try {
    const session = await getServerSession(authOptions);
    
    // เสียง logging เสียงตรวจสอบ session
    console.log('Session in DELETE:', session);
    console.log('User role from session:', session?.user?.role);
    
    if (!session) {
      return Response.json({ error: 'Not authenticated' }, { status: 401 });
    }
    
    // สร้างการเชื่อมต่อกับฐานข้อมูล
    const pool = await connectToDatabase();
    
    // ตรวจสอบ role ของใช้จากฐานข้อมูล
    const userCheckResult = await pool.request()
      .input('email', session.user.email)
      .query(`
        SELECT Role
        FROM dbo.PMS3_Users
        WHERE Email = @email
      `);
      
    if (userCheckResult.recordset.length === 0) {
      return Response.json({ error: 'User not found in database' }, { status: 404 });
    }
    
    const currentUserRole = userCheckResult.recordset[0].Role;
    console.log('User role from database:', currentUserRole);
    
    // ตรวจสอบ role จากฐานข้อมูล
    if (currentUserRole !== 'Admin') {
      return Response.json({ error: 'Admin permission required' }, { status: 403 });
    }

    const { id } = params;
    console.log('Attempting to delete user with ID:', id);
    
    // ตรวจสอบว่าใช้จะลบอยู่ไม่
    const userResult = await pool.request()
      .input('id', id)
      .query(`
        SELECT Email, ProfileImagePath
        FROM dbo.PMS3_Users
        WHERE Id = @id
      `);
    
    if (userResult.recordset.length === 0) {
      return Response.json({ error: 'User not found' }, { status: 404 });
    }
    
    const user = userResult.recordset[0];
    
    // ป้องการลบ
    if (user.Email === session.user.email) {
      return Response.json({ error: 'Cannot delete your own account' }, { status: 403 });
    }
    
    // ลบรูปโปรไฟล์ถ้า
    if (user.ProfileImagePath) {
      try {
        const filePath = path.join(process.cwd(), 'public', user.ProfileImagePath);
        await fs.unlink(filePath);
      } catch (error) {
        console.error('Error deleting profile image:', error);
        // งต่อแม้ลบรูปไม่สำเร็จ
      }
    }
    
    // ลบการเปลี่ยนผ่าน
    try {
      await pool.request()
        .input('email', user.Email)
        .query(`
          DELETE FROM dbo.PMS3_UserPasswordHistory
          WHERE Email = @email
        `);
    } catch (error) {
      console.error('Error deleting password history:', error);
      return Response.json({ error: 'Failed to delete user password history' }, { status: 500 });
    }
    
    // ลบ้ใช้
    try {
      await pool.request()
        .input('id', id)
        .query(`
          DELETE FROM dbo.PMS3_Users
          WHERE Id = @id
        `);
    } catch (error) {
      console.error('Error deleting user from database:', error);
      return Response.json({ error: 'Failed to delete user from database' }, { status: 500 });
    }
    
    return Response.json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Error in DELETE user function:', error);
    return Response.json({ error: `Failed to delete user: ${error.message}` }, { status: 500 });
  }
}







