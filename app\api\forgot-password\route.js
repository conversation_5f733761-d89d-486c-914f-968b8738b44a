import { NextResponse } from "next/server";
import { connectToDatabase } from '@/lib/db';
import { sendResetEmail } from "@/app/utils/sendEmail";
import crypto from "crypto";

// สร้าง token ความยาว 64 ตัวอักษร (32 bytes)
const token = crypto.randomBytes(32).toString("hex");

export async function POST(req) {
  const { email } = await req.json();

  try {
    const pool = await connectToDatabase();

    const result = await pool
      .request()
      .input("email", email)
      .query("SELECT Id FROM dbo.PMS3_Users WHERE Email = @email AND Active=1");

    if (result.recordset.length === 0) {
      return NextResponse.json({ message: "Email not found." }, { status: 404 });
    }

    const userId = result.recordset[0].Id;
    const expireTime = new Date(Date.now() + 60 * 60 * 1000);

    await pool
      .request()
      .input("token", token)
      .input("expireTime", expireTime)
      .input("userId", userId)
      .query(`
    UPDATE dbo.PMS3_Users 
    SET ResetToken = @token, ResetTokenExpire = @expireTime 
    WHERE Id = @userId
  `);

    const resetLink = `http://localhost:3000/reset-password?email=${encodeURIComponent(email)}&token=${encodeURIComponent(token)}`;
    const sendResult = await sendResetEmail(email, resetLink);

    if (!sendResult.success) {
      return NextResponse.json({ message: "Failed to send email." }, { status: 500 });
    }

    return NextResponse.json({ message: `Reset link sent to ${email} <br> Please check your e-mail, then change your password within 60 minutes.` });

  } catch (error) {
    console.error(error);
    return NextResponse.json({ message: "Something went wrong." }, { status: 500 });
  }
}
