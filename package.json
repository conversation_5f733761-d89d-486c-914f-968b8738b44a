{"name": "nextjs-user-mgn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "form-data": "^4.0.2", "formidable": "^3.5.4", "lucide-react": "^0.511.0", "mssql": "^11.0.1", "next": "15.3.3", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-easy-crop": "^5.4.2", "react-icons": "^5.5.0", "sweetalert2": "^11.22.0", "uuid": "^11.1.0", "zxcvbn": "^4.4.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4"}}