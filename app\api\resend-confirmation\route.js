import { connectToDatabase } from '@/lib/db';
import { v4 as uuidv4 } from 'uuid';
import { NextResponse } from 'next/server';

export async function POST(req) {
  try {
    const { email } = await req.json();

    if (!email) {
      return NextResponse.json({ message: 'Email is required' }, { status: 400 });
    }

    const pool = await connectToDatabase();

    // ตรวจสอบว่ามีผู้ใช้ที่ยังไม่ได้ยืนยันหรือไม่
    const userResult = await pool.request()
      .input('email', email)
      .query(`
        SELECT Id, Email, FullName, Active
        FROM dbo.PMS3_Users
        WHERE Email = @email
      `);

    if (userResult.recordset.length === 0) {
      return NextResponse.json(
        { message: 'User not found' },
        { status: 404 }
      );
    }

    const user = userResult.recordset[0];

    // ถ้าบัญชีถูกยืนยันแล้ว
    if (user.Active === 1 || user.Active === true) {
      return NextResponse.json(
        { message: 'Account is already activated' },
        { status: 400 }
      );
    }

    // สร้าง token ใหม่
    const confirmationToken = uuidv4();
    const tokenExpiry = new Date();
    tokenExpiry.setHours(tokenExpiry.getHours() + 24); // หมดอายุใน 24 ชั่วโมง

    // อัปเดต token ในฐานข้อมูล
    await pool.request()
      .input('email', email)
      .input('token', confirmationToken)
      .input('expiry', tokenExpiry)
      .query(`
        UPDATE dbo.PMS3_Users
        SET ResetToken = @token, ResetTokenExpire = @expiry
        WHERE Email = @email
      `);

    // ส่งอีเมลยืนยันใหม่
    const confirmUrl = `${process.env.NEXT_PUBLIC_APP_URL}/confirm-email?token=${confirmationToken}`;
    
    await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/send-confirmation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email,
        name: user.FullName,
        confirmationToken,
        confirmUrl
      }),
    });

    return NextResponse.json({
      message: 'Confirmation email sent successfully'
    });
  } catch (error) {
    console.error('Error resending confirmation email:', error);
    return NextResponse.json(
      { message: 'Failed to send confirmation email' },
      { status: 500 }
    );
  }
}