@import "tailwindcss";

/* :root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

/* body { */
  /* background: var(--background);
  color: var(--foreground); */
  /* font-family: Arial, Helvetica, sans-serif; */
/* } */

/* Toggle Switch Styles */
.toggle-checkbox {
  right: 0;
  z-index: 5;
  transition: all 0.3s;
  border-color: #e2e8f0;
  top: 0;
}

.toggle-checkbox:checked {
  right: 6px;
  border-color: #10b981;
}

.toggle-label {
  transition: background-color 0.3s;
}

.toggle-checkbox:checked + .toggle-label {
  background-color: #10b981;
}


