import { connectToDatabase } from '@/lib/db';
import { getServerSession, update } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import fs from 'fs/promises';
import path from 'path';

export async function POST(req) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return new Response(JSON.stringify({ message: 'Unauthorized' }), { status: 401 });
    }

    const { email } = await req.json();
    
    // ตรวจสอบว่า email ตรงกับ email ใน session
    if (email !== session.user.email) {
      return new Response(JSON.stringify({ message: 'Unauthorized' }), { status: 401 });
    }

    const pool = await connectToDatabase();
    
    // ข้อมูลภาพ
    const userResult = await pool.request()
      .input('email', email)
      .query(`
        SELECT ProfileImagePath
        FROM dbo.PMS3_Users
        WHERE Email = @email
      `);
    
    if (userResult.recordset.length === 0) {
      return new Response(JSON.stringify({ message: 'User not found' }), { status: 404 });
    }
    
    const user = userResult.recordset[0];
    
    // ลบไฟล์ภาพจากเซิร์ฟเวอร์ (ถ้ามี)
    if (user.ProfileImagePath) {
      try {
        const filePath = path.join(process.cwd(), 'public', user.ProfileImagePath);
        await fs.unlink(filePath);
      } catch (error) {
        console.error('Error deleting profile image file:', error);
        // ไม่ต้อง return error เพราะอาจจะเป็นไฟล์ไม่อยู่
      }
    }
    
    // ปเดตฐานข้อมูลให้ล้างค่าภาพ
    await pool.request()
      .input('email', email)
      .query(`
        UPDATE dbo.PMS3_Users
        SET ProfileImagePath = NULL,
            ProfileImageContentType = NULL,
            UpdatedDate = GETDATE()
        WHERE Email = @email
      `);
    
    // ลบส่วนการปเดต session เนื่องจากไม่สามารถใช้ update ได้ใน server
    // ให้ปเดต session ใน client แทน
    
    return new Response(JSON.stringify({ 
      message: 'Profile picture removed successfully',
      user: {
        name: session.user.name,
        email: session.user.email,
        image: null
      }
    }), { status: 200 });
    
  } catch (error) {
    console.error('Profile picture removal error:', error);
    return new Response(JSON.stringify({ message: 'Error removing profile picture' }), { status: 500 });
  }
}
