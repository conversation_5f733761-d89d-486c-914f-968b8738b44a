"use client";
import { useState } from "react";
import Swal from "sweetalert2";

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const res = await fetch("/api/forgot-password", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email }),
      });

      const data = await res.json();

      if (res.ok) {
        Swal.fire({
          icon: "success",
          title: "Reset link sent!",
          html: `
            <p>Reset link has been sent to <strong>${email}</strong></p>
            <p class="mt-2">Please check your email and follow the link to reset your password.</p>
            <p class="mt-2 text-red-500">Note: The reset link will expire in 1 hour.</p>
          `,
          confirmButtonColor: "#3085d6",
        });
      } else {
        Swal.fire({
          icon: "error",
          title: "Oops...",
          text: data.message || "Something went wrong.",
          confirmButtonColor: "#d33",
        });
      }
    } catch (error) {
      console.error(error);
      Swal.fire({
        icon: "error",
        title: "Oops...",
        text: "Something went wrong. Please try again.",
        confirmButtonColor: "#d33",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 p-4">
      <div className="max-w-md mx-auto p-8 bg-white rounded-lg shadow-2xl">
        <h1 className="text-2xl font-bold mb-4">Forgot Password?</h1>

        <form onSubmit={handleSubmit} className="space-y-4">
          <p className="mt-6">Input your registered email:</p>
          <input
            type="email"
            required
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter your registered email"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 duration-150 ease-in-out placeholder:text-gray-300"
          />

          <div className="flex items-stretch gap-4">
            <button
              type="submit"
              disabled={loading}
              className={`w-full py-2 rounded text-white transition ${loading
                  ? "bg-gray-400 cursor-not-allowed"
                  : "bg-blue-600 hover:bg-blue-700 cursor-pointer"
                }`}
            >
              {loading ? "Sending..." : "Send Reset Link"}
            </button>
          </div>
        </form>

        <div className="mt-6 text-gray-300">
          You're already has authorization?
          <a
            href="/login"
            className=" text-gray-400 hover:text-orange-500 transition cursor-pointer"
          >
            {" "}
            Sign In
          </a>
        </div>
      </div>
    </div>
  );
}
