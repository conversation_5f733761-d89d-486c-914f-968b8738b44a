// app/api/auth/[...nextauth]/route.js
import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { connectToDatabase } from '@/lib/db';
import bcrypt from 'bcryptjs';

export const authOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        try {
          const pool = await connectToDatabase();
          
          // ค้นหา ้ใช้จาก และ ่ม ProfileImagePath ในการ query
          const result = await pool.request()
            .input('email', credentials.email)
            .query(`
              SELECT Id, Email, Password, FullName, Role, Active, ProfileImagePath
              FROM dbo.PMS3_Users
              WHERE Email = @email
            `);
          
          if (result.recordset.length === 0) {
            throw new Error('No user found with this email');
          }
          
          const user = result.recordset[0];
          
          // ตรวจสอบ ผ่าน
          const isValid = await bcrypt.compare(credentials.password, user.Password);
          
          if (!isValid) {
            throw new Error('Invalid password');
          }
          
          // ตรวจสอบว่า แล้ว ไม่
          if (user.Active === 0 || user.Active === false) {
            throw new Error('Your account has not been confirmed yet. Please check your email for the confirmation link.');
          }
          
          // เล่ม image ในข้อมูล ่ส่ง
          return {
            id: user.Id,
            email: user.Email,
            name: user.FullName,
            role: user.Role,
            image: user.ProfileImagePath
          };
        } catch (error) {
          console.error('Authorization error:', error);
          throw error;
        }
      },
    }),
  ],
  pages: {
    signIn: '/login',
    error: '/login',
  },
  session: {
    strategy: 'jwt',
    maxAge: 24 * 60 * 60, // 24 ชั่วโมง
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.role = user.role;
        token.image = user.image; // เล่ม image ในข้อมูล token
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id;
        session.user.role = token.role;
        session.user.image = token.image; // เล่ม image ในข้อมูล session
      }
      return session;
    }
  },
  secret: process.env.NEXTAUTH_SECRET,
};

const handler = NextAuth(authOptions);
export { handler as GET, handler as POST };
