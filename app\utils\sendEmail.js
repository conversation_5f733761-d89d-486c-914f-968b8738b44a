import nodemailer from 'nodemailer';

// สร้าง transporter
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_SERVER_HOST,
  port: process.env.EMAIL_SERVER_PORT,
  secure: process.env.EMAIL_SERVER_SECURE === 'true',
  auth: {
    user: process.env.EMAIL_SERVER_USER,
    pass: process.env.EMAIL_SERVER_PASSWORD,
  },
});

// ฟังก์ชันส่งอีเมลยืนยัน
export async function sendConfirmationEmail(email, name, token, confirmUrl) {
  const mailOptions = {
    from: `"PMS System" <${process.env.EMAIL_FROM}>`,
    to: email,
    subject: 'Confirm Your Account',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <h2 style="color: #333; text-align: center;">Welcome to PMS System!</h2>
        <p>Hello ${name},</p>
        <p>Thank you for registering. Please confirm your email address by clicking the button below:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${confirmUrl}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">
            Confirm Email
          </a>
        </div>
        <p>If the button doesn't work, you can also click on the link below or copy it to your browser:</p>
        <p><a href="${confirmUrl}">${confirmUrl}</a></p>
        <p>This link will expire in 24 hours.</p>
        <p>If you didn't create an account, you can safely ignore this email.</p>
        <p>Best regards,<br>PMS System Team</p>
      </div>
    `,
  };

  return transporter.sendMail(mailOptions);
}

// ฟังก์ชันส่งอีเมลสำหรับการรีเซ็ตรหัสผ่าน
export async function sendResetEmail(email, name, resetToken, resetUrl) {
  const mailOptions = {
    from: `"PMS System" <${process.env.EMAIL_FROM}>`,
    to: email,
    subject: 'Reset Your Password',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <h2 style="color: #333; text-align: center;">Reset Your Password</h2>
        <p>Hello ${name},</p>
        <p>You have requested to reset your password. Please click the button below to proceed:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${resetUrl}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">
            Reset Password
          </a>
        </div>
        <p>If the button doesn't work, you can also click on the link below or copy it to your browser:</p>
        <p><a href="${resetUrl}">${resetUrl}</a></p>
        <p>This link will expire in 24 hours.</p>
        <p>If you didn't request a password reset, you can ignore this email.</p>
        <p>Best regards,<br>PMS System Team</p>
      </div>
    `,
  };

  return transporter.sendMail(mailOptions);
}
