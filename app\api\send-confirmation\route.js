import { NextResponse } from 'next/server';
import { sendConfirmationEmail } from '@/app/utils/sendEmail';

export async function POST(req) {
  try {
    const { email, name, confirmationToken, confirmUrl } = await req.json();
    
    if (!email || !name || !confirmationToken || !confirmUrl) {
      return NextResponse.json(
        { message: 'Email, name, confirmation token and URL are required' },
        { status: 400 }
      );
    }
    
    // ส่ง email ยืนยัน
    await sendConfirmationEmail(email, name, confirmationToken, confirmUrl);
    
    return NextResponse.json(
      { message: 'Confirmation email sent successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error sending confirmation email:', error);
    return NextResponse.json(
      { message: 'Failed to send confirmation email' },
      { status: 500 }
    );
  }
}











