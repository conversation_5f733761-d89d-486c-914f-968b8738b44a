'use client';
import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Swal from 'sweetalert2';
import Cropper from 'react-easy-crop';
import { getCroppedImg } from '../utils/cropImage';

export default function ProfilePage() {
    const { data: session, update } = useSession();
    const router = useRouter();
    const [fullname, setFullname] = useState('');
    const [currentPassword, setCurrentPassword] = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [profileImage, setProfileImage] = useState(null);
    const [previewImage, setPreviewImage] = useState(null);
    const [showCropper, setShowCropper] = useState(false);
    const [crop, setCrop] = useState({ x: 0, y: 0 });
    const [zoom, setZoom] = useState(1);
    const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);
    const [strength, setStrength] = useState(0);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [userData, setUserData] = useState(null);
    const [validation, setValidation] = useState({
        length: false,
        uppercase: false,
        lowercase: false,
        number: false,
        special: false,
    });

    // ข้อมูลใช้จากฐานข้อมูล
    const fetchUserData = async () => {
        if (session?.user?.email) {
            try {
                const response = await fetch('/api/profile/get', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email: session.user.email }),
                });
                
                if (response.ok) {
                    const data = await response.json();
                    setUserData(data.user);
                    setFullname(data.user.fullName || '');
                    setPreviewImage(data.user.profileImagePath || null);
                }
            } catch (error) {
                console.error('Error fetching user data:', error);
            }
        }
    };

    useEffect(() => {
        fetchUserData();
    }, [session]);

    const checkPasswordStrength = (password) => {
        let score = 0;
        
        // ตรวจสอบเงื่อนไขและ<|im_start|>ปเดต validation state
        const newValidation = {
            length: password.length >= 8,
            uppercase: /[A-Z]/.test(password),
            lowercase: /[a-z]/.test(password),
            number: /\d/.test(password),
            special: /[@$!%*#?&^+=]/.test(password),
        };
        
        setValidation(newValidation);
        
        // คำนวณคะแนนความแข็งแรง
        if (newValidation.length) score++;
        if (newValidation.uppercase) score++;
        if (newValidation.lowercase) score++;
        if (newValidation.number) score++;
        if (newValidation.special) score++;
        
        setStrength(score);
        return score;
    };

    const handleImageChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            if (file.size > 5 * 1024 * 1024) {
                Swal.fire({
                    icon: 'error',
                    title: 'File too large',
                    text: 'Please select an image under 5MB'
                });
                return;
            }
            const reader = new FileReader();
            reader.onload = () => {
                setPreviewImage(reader.result);
                setShowCropper(true);
            };
            reader.readAsDataURL(file);
        }
    };

    const onCropComplete = (croppedArea, croppedAreaPixels) => {
        setCroppedAreaPixels(croppedAreaPixels);
    };

    const createImage = (url) => new Promise((resolve, reject) => {
        const image = new Image();
        image.addEventListener('load', () => resolve(image));
        image.addEventListener('error', error => reject(error));
        image.src = url;
    });

    const getCroppedImg = async () => {
        try {
            const image = await createImage(previewImage);
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            // สร้าง canvas ขนาดเท่าภาพจะครอป
            canvas.width = croppedAreaPixels.width;
            canvas.height = croppedAreaPixels.height;

            ctx.drawImage(
                image,
                croppedAreaPixels.x,
                croppedAreaPixels.y,
                croppedAreaPixels.width,
                croppedAreaPixels.height,
                0,
                0,
                croppedAreaPixels.width,
                croppedAreaPixels.height
            );

            return new Promise((resolve) => {
                canvas.toBlob((blob) => {
                    resolve(blob);
                }, 'image/jpeg', 0.95); // เก็บเป็น 95%
            });
        } catch (e) {
            console.error('Error cropping image:', e);
            return null;
        }
    };

    const handleSaveCrop = async () => {
        const croppedImage = await getCroppedImg();
        if (croppedImage) {
            setProfileImage(croppedImage);
            setPreviewImage(URL.createObjectURL(croppedImage));
            setShowCropper(false);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        if (newPassword && strength < 2) {
            const result = await Swal.fire({
                icon: 'warning',
                title: 'Weak Password',
                text: 'Your password is weak. Do you want to continue?',
                showCancelButton: true,
                confirmButtonColor: '#f97316',
                cancelButtonText: 'Cancel'
            });
            if (!result.isConfirmed) {
                return;
            }
        }
        if (newPassword !== confirmPassword) {
            Swal.fire({
                icon: 'error',
                title: 'Password Mismatch',
                text: 'New password and confirmation do not match'
            });
            return;
        }
        setIsSubmitting(true);
        const formData = new FormData();
        formData.append('fullname', fullname);
        if (currentPassword) formData.append('currentPassword', currentPassword);
        if (newPassword) formData.append('newPassword', newPassword);
        if (profileImage) formData.append('profileImage', profileImage);
        try {
            const response = await fetch('/api/profile/update', {
                method: 'POST',
                body: formData
            });
            const data = await response.json();
            if (response.ok) {
                // แสดงข้อความแจ้ง
                Swal.fire({
                    icon: 'success',
                    title: 'Profile Updated',
                    text: 'Your profile has been updated successfully.',
                    confirmButtonColor: '#3085d6',
                    confirmButtonText: 'OK'
                }).then(() => {
                    setCurrentPassword('');
                    setNewPassword('');
                    setConfirmPassword('');
                    setStrength(0);
                    
                    // เรียกใช้ fetchUserData เอ่อโหลดข้อมูลใหม่
                    fetchUserData();
                });
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: error.message
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    // เล่ม์ก์ ลบรูปโปรไฟล์
    const removeImage = () => {
        setProfileImage(null);
        setPreviewImage(session?.user?.image || null);
        setShowCropper(false);
        // เซ็ต input file
        const fileInput = document.querySelector('input[type="file"]');
        if (fileInput) {
            fileInput.value = '';
        }
    };

    // เล่ม์ก์ ลบรูปโปรไฟล์ถาวร
    const handleRemoveProfilePicture = async () => {
        // ถามก่อนลบ
        const result = await Swal.fire({
            title: 'Remove Profile Picture?',
            text: 'This will permanently remove your profile picture. Continue?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#ef4444',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, remove it',
            cancelButtonText: 'Cancel'
        });

        if (result.isConfirmed) {
            try {
                setIsSubmitting(true);
                const response = await fetch('/api/profile/remove-picture', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email: session.user.email }),
                });

                const data = await response.json();

                if (response.ok) {
                    // เซ็ตรูปภาพในหน้า
                    setProfileImage(null);
                    setPreviewImage(null);
                    
                    // ปเดต session ใน อง client
                    await update({
                        ...session,
                        user: {
                            ...session.user,
                            image: null
                        }
                    });
                    
                    // แสดงข้อความสำเร็จ
                    Swal.fire({
                        icon: 'success',
                        title: 'Profile Picture Removed',
                        text: 'Your profile picture has been removed successfully.',
                        confirmButtonColor: '#3085d6'
                    }).then(() => {
                        // เรียกใช้ fetchUserData เอ่อโหลดข้อมูลใหม่
                        fetchUserData();
                    });
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: error.message || 'Failed to remove profile picture',
                    confirmButtonColor: '#ef4444'
                });
            } finally {
                setIsSubmitting(false);
            }
        }
    };

    const getStrengthLabel = () => {
        switch (strength) {
            case 0: return 'Very Weak';
            case 1: return 'Weak';
            case 2: return 'Fair';
            case 3: return 'Good';
            case 4: return 'Strong';
            case 5: return 'Very Strong';
            default: return '';
        }
    };
 
    return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-blue-50">
            <div className="bg-white my-3 p-4 rounded-xl shadow-lg w-full max-w-md border border-gray-100">
                <h2 className="text-3xl font-bold mb-6 text-center text-gray-800">Profile</h2>
                
                {/* ส่วนแสดงข้อมูลโปรไฟล์ */}
                <div className="-m-4 flex flex-col items-center sticky top-15 z-10 bg-white p-4" style={{boxShadow: '0 2px 2px -1px rgba(0, 0, 0, 0.1)'}}>
                    <div className="mb-2 relative">
                        {previewImage ? (
                            <img 
                                src={previewImage} 
                                alt="Current Profile" 
                                className="w-24 h-24 rounded-full object-cover border-2 border-blue-500 shadow-md"
                            />
                        ) : userData?.profileImagePath ? (
                            <img 
                                src={userData.profileImagePath} 
                                alt="Current Profile" 
                                className="w-24 h-24 rounded-full object-cover border-2 border-blue-500 shadow-md"
                            />
                        ) : (
                            <div className="w-24 h-24 rounded-full bg-gray-200 flex items-center justify-center border-2 border-blue-500 shadow-md">
                                <span className="text-3xl text-gray-400">
                                    {userData?.fullName?.charAt(0) || session?.user?.name?.charAt(0) || '?'}
                                </span>
                            </div>
                        )}
                        
                        {/* เล่ม์ก์ ลบรูปโปรไฟล์ถาวร */}
                        {(userData?.profileImagePath || previewImage) && (
                            <button
                                type="button"
                                onClick={handleRemoveProfilePicture}
                                className="cursor-pointer absolute bottom-0 right-2 bg-red-400 text-white rounded-full p-1 shadow-md hover:bg-red-600 transition-colors"
                                title="Remove profile picture permanently"
                                disabled={isSubmitting}
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        )}
                    </div>
                    <h3 className="text-xl font-semibold text-gray-500">{userData?.fullName || session?.user?.name || 'User'}</h3>
                    <p className="text-gray-300">{userData?.email || session?.user?.email || 'No email'}</p>
                </div>
                
                <div className="border-t border-gray-200 pt-6 mb-6">
                    <h3 className="text-lg font-semibold mb-4 text-gray-700">Update Your Profile</h3>
                </div>
                
                <form onSubmit={handleSubmit} className="space-y-6">
                    <div>
                        <label className="block text-sm font-semibold mb-2">Full Name</label>
                        <input
                            type="text"
                            value={fullname}
                            onChange={(e) => setFullname(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            required
                        />
                    </div>
                    
                    <div>
                        <label className="block text-sm font-semibold mb-2">Current Password</label>
                        <input
                            type="password"
                            value={currentPassword}
                            onChange={(e) => setCurrentPassword(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                    
                    <div>
                        <label className="block text-sm font-semibold mb-2">New Password</label>
                        <input
                            type="password"
                            value={newPassword}
                            onChange={(e) => {
                                setNewPassword(e.target.value);
                                checkPasswordStrength(e.target.value);
                            }}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                        {newPassword && (
                            <>
                                <div className="mt-1">
                                    <div className="h-1 w-full bg-gray-200 rounded">
                                        <div
                                            className={`h-1 rounded transition-all duration-300
                                                ${strength <= 1 ? 'bg-red-400 w-1/5' :
                                                strength === 2 ? 'bg-orange-400 w-2/5' :
                                                strength === 3 ? 'bg-yellow-400 w-3/5' :
                                                strength === 4 ? 'bg-green-400 w-4/5' :
                                                'bg-green-600 w-full'}`}
                                        ></div>
                                    </div>
                                    <div className="mt-1 text-xs text-gray-600">
                                        Password strength: <b>{getStrengthLabel()}</b>
                                    </div>
                                </div>
                                
                                {/* เusahaส่วนแสดงเงือนไขส่งผ่าน */}
                                <div className="mt-2 space-y-1 text-sm">
                                    <div className={validation.length ? 'text-green-600' : 'text-gray-500'}>
                                        {validation.length ? '✔' : '✖'} At least 8 characters
                                    </div>
                                    <div className={validation.uppercase ? 'text-green-600' : 'text-gray-500'}>
                                        {validation.uppercase ? '✔' : '✖'} Uppercase letter (A-Z)
                                    </div>
                                    <div className={validation.lowercase ? 'text-green-600' : 'text-gray-500'}>
                                        {validation.lowercase ? '✔' : '✖'} Lowercase letter (a-z)
                                    </div>
                                    <div className={validation.number ? 'text-green-600' : 'text-gray-500'}>
                                        {validation.number ? '✔' : '✖'} Number (0-9)
                                    </div>
                                    <div className={validation.special ? 'text-green-600' : 'text-gray-500'}>
                                        {validation.special ? '✔' : '✖'} Special character (@$!%*#?&^+=)
                                    </div>
                                </div>
                            </>
                        )}
                    </div>
                    <div>
                        <label className="block text-sm font-semibold mb-2">Confirm New Password</label>
                        <input
                            type="password"
                            value={confirmPassword}
                            onChange={(e) => setConfirmPassword(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-semibold mb-2">
                            Profile Image
                            <span className="text-xs text-gray-500 font-normal"> (Max 5 MB.)</span>
                        </label>
                        <div className="flex items-center">
                            <label className="flex-1 cursor-pointer px-4 py-2 bg-blue-50 text-blue-600 rounded-md border border-blue-200 hover:bg-blue-100 transition-colors text-center">
                                <span>Choose File</span>
                                <input
                                    type="file"
                                    accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
                                    onChange={handleImageChange}
                                    className="hidden"
                                />
                            </label>
                            {profileImage && (
                                <button
                                    type="button"
                                    onClick={removeImage}
                                    className="cursor-pointer ml-2 px-3 py-2 bg-red-50 text-red-600 rounded-md border border-red-200 hover:bg-red-100"
                                >
                                    Remove
                                </button>
                            )}
                        </div>
                        {previewImage && !showCropper && (
                            <div className="mt-4 flex flex-col items-center gap-2">
                                <img
                                    src={previewImage}
                                    alt="Profile Preview"
                                    className="w-32 h-32 rounded-full object-cover border shadow"
                                />
                                <button
                                    type="button"
                                    onClick={() => setShowCropper(true)}
                                    className="cursor-pointer px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
                                >
                                    Edit Image
                                </button>
                            </div>
                        )}
                    </div>
                    {showCropper && (
                        <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50">
                            <div className="bg-white p-4 rounded-lg w-[90%] max-w-md">
                                <h3 className="text-lg font-semibold mb-3">Edit Profile Image</h3>
                                <div className="relative h-64 mb-4">
                                    <Cropper
                                        image={previewImage}
                                        crop={crop}
                                        zoom={zoom}
                                        aspect={1}
                                        onCropChange={setCrop}
                                        onZoomChange={setZoom}
                                        onCropComplete={onCropComplete}
                                    />
                                </div>
                                <div className="flex flex-col gap-2 mb-4">
                                    <div className="flex items-center gap-2">
                                        <span className="text-xs text-gray-600">Zoom:</span>
                                        <input
                                            type="range"
                                            min={1}
                                            max={3}
                                            step={0.1}
                                            value={zoom}
                                            onChange={(e) => setZoom(Number(e.target.value))}
                                            className="flex-1"
                                        />
                                    </div>
                                </div>
                                <div className="flex justify-end gap-2">
                                    <button
                                        type="button"
                                        onClick={() => setShowCropper(false)}
                                        className="px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300 cursor-pointer"
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        type="button"
                                        onClick={handleSaveCrop}
                                        className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 cursor-pointer"
                                    >
                                        Save
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}
                    <div className="flex gap-3 pt-2">
                        <button
                            type="button"
                            onClick={() => router.push('/')}
                            className="cursor-pointer flex-1 py-2 px-4 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition duration-200"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            disabled={isSubmitting}
                            className={`cursor-pointer flex-1 py-2 px-4 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition duration-200 ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
                            onClick={async () => {
                                // Update session data after successful profile update
                                await update({
                                    ...session,
                                    user: {
                                        ...session.user,
                                        name: fullname,
                                        image: previewImage || userData?.profileImagePath
                                    }
                                });
                            }}
                        >
                            {isSubmitting ? 'Updating...' : 'Update Profile'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}
