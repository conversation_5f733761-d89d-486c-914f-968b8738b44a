import { connectToDatabase } from '@/lib/db';
import bcrypt from 'bcryptjs';

export async function POST(req) {
  try {
    const { email, password, fullname } = await req.json();
    const pool = await connectToDatabase();

    // check duplicate email
    const existing = await pool.request()
      .input('email', email)
      .query('SELECT * FROM dbo.PMS3_Users WHERE Email = @email');

    if (existing.recordset.length > 0) {
      return new Response(JSON.stringify({ message: 'Email already exists' }), { status: 400 });
    }

    // get 3 latest password hashes from PasswordHistory
    // const lastPasswords = await pool.request()
    //   .input('email', email)
    //   .query(`
    //     SELECT TOP 3 PasswordHash
    //     FROM dbo.UserPasswordHistory h
    //     JOIN dbo.PMS3_Users u ON u.Id = h.UserId
    //     WHERE u.Email = @email
    //     ORDER BY h.CreatedAt DESC
    //   `);

    // check if new password matches any old
    // for (const row of lastPasswords.recordset) {
    //   const match = await bcrypt.compare(password, row.PasswordHash);
    //   if (match) {
    //     return new Response(JSON.stringify({ message: 'New password must not match last 3 passwords.' }), { status: 400 });
    //   }
    // }

    const hashedPassword = await bcrypt.hash(password, 10);

    // insert new user
    const insertUser = await pool.request()
      .input('email', email)
      .input('password', hashedPassword)
      .input('fullname', fullname)
      .input('role', 'User')
      .query(`
        INSERT INTO dbo.PMS3_Users (Email, Password, FullName, Role)
        OUTPUT INSERTED.Id
        VALUES (@email, @password, @fullname, @role)
      `);

    const newUserId = insertUser.recordset[0].Id;

    // insert password into history
    await pool.request()
      .input('userId', newUserId)
      .input('passwordHash', hashedPassword)
      .query(`
        INSERT INTO dbo.UserPasswordHistory (UserId, PasswordHash, CreatedAt)
        VALUES (@userId, @passwordHash, GETDATE())
      `);

    return new Response(JSON.stringify({ message: 'User created' }), { status: 201 });
  } catch (err) {
    console.error(err);
    return new Response(JSON.stringify({ message: 'Error registering user' }), { status: 500 });
  }
}
